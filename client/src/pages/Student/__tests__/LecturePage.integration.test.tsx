import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import LecturePage from '../LecturePage';
import { Toaster } from '@/components/ui/toaster';

// Mock all the API hooks
vi.mock('@/redux/features/auth/authApi', () => ({
  useGetMeQuery: vi.fn()
}));

vi.mock('@/redux/features/course/course.api', () => ({
  useGetCourseByIdQuery: vi.fn()
}));

vi.mock('@/redux/features/lecture/lectureApi', () => ({
  useGetLectureByCourseIdQuery: vi.fn(),
  useGetLectureByIdQuery: vi.fn()
}));

vi.mock('@/redux/features/student/studentApi', () => ({
  useGetCourseProgressQuery: vi.fn(),
  useMarkLectureCompleteMutation: vi.fn(),
  useGetEnrolledCoursesQuery: vi.fn()
}));

// Mock CourseLayout
vi.mock('@/components/Course/CourseLayout', () => ({
  default: ({ onMarkComplete, isMarkingComplete, isCompleted }: any) => (
    <div data-testid="course-layout">
      <div>Course Layout</div>
      <div>Is Completed: {isCompleted ? 'Yes' : 'No'}</div>
      <div>Is Marking Complete: {isMarkingComplete ? 'Yes' : 'No'}</div>
      <button onClick={onMarkComplete} data-testid="mark-complete-btn">
        Mark Complete
      </button>
    </div>
  )
}));

// Mock CourseCompletionModal
vi.mock('@/components/Course/CourseCompletionModal', () => ({
  default: ({ open, courseTitle, percentage }: any) => (
    open ? (
      <div data-testid="completion-modal">
        <div>Course: {courseTitle}</div>
        <div>Progress: {percentage}%</div>
      </div>
    ) : null
  )
}));

// Mock react-router-dom params
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({
      courseId: 'course-123',
      lectureId: 'lecture-456'
    }),
    useNavigate: () => vi.fn()
  };
});

// Create mock store
const createMockStore = () => configureStore({
  reducer: {
    auth: (state = { user: null }) => state,
    course: (state = {}) => state,
    lecture: (state = {}) => state,
    student: (state = {}) => state,
    player: (state = { positions: {} }) => state
  }
});

const renderWithProviders = (component: React.ReactElement) => {
  const store = createMockStore();
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
        <Toaster />
      </BrowserRouter>
    </Provider>
  );
};

describe('LecturePage Integration Tests', () => {
  const mockUserData = {
    data: {
      _id: 'user-123',
      name: 'Test User',
      email: '<EMAIL>'
    }
  };

  const mockCourseData = {
    data: {
      _id: 'course-123',
      title: 'Test Course',
      description: 'A test course'
    }
  };

  const mockLectureData = {
    data: {
      _id: 'lecture-456',
      lectureTitle: 'Test Lecture',
      instruction: 'Test instruction',
      videoUrl: 'https://example.com/video.mp4',
      duration: 3600,
      courseId: 'course-123'
    }
  };

  const mockProgressData = {
    data: {
      percentage: 75,
      lectureProgress: [
        { lectureId: 'lecture-456', isCompleted: false }
      ]
    }
  };

  const mockEnrolledCoursesData = {
    data: [
      { courseId: 'course-123', enrolledAt: new Date() }
    ]
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock implementations
    const { useGetMeQuery } = require('@/redux/features/auth/authApi');
    const { useGetCourseByIdQuery } = require('@/redux/features/course/course.api');
    const { useGetLectureByCourseIdQuery, useGetLectureByIdQuery } = require('@/redux/features/lecture/lectureApi');
    const { useGetCourseProgressQuery, useMarkLectureCompleteMutation, useGetEnrolledCoursesQuery } = require('@/redux/features/student/studentApi');

    useGetMeQuery.mockReturnValue({
      data: mockUserData,
      isLoading: false,
      error: null
    });

    useGetCourseByIdQuery.mockReturnValue({
      data: mockCourseData,
      isLoading: false,
      error: null
    });

    useGetLectureByCourseIdQuery.mockReturnValue({
      data: { data: [mockLectureData.data] },
      isLoading: false,
      error: null
    });

    useGetLectureByIdQuery.mockReturnValue({
      data: mockLectureData,
      isLoading: false,
      error: null,
      refetch: vi.fn()
    });

    useGetCourseProgressQuery.mockReturnValue({
      data: mockProgressData,
      isLoading: false,
      error: null,
      refetch: vi.fn()
    });

    useMarkLectureCompleteMutation.mockReturnValue([
      vi.fn().mockResolvedValue({ data: { success: true } }),
      { isLoading: false }
    ]);

    useGetEnrolledCoursesQuery.mockReturnValue({
      data: mockEnrolledCoursesData,
      isLoading: false,
      error: null
    });

    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders lecture page successfully with all data', async () => {
    renderWithProviders(<LecturePage />);

    await waitFor(() => {
      expect(screen.getByTestId('course-layout')).toBeInTheDocument();
    });

    expect(screen.getByText('Course Layout')).toBeInTheDocument();
    expect(screen.getByText('Is Completed: No')).toBeInTheDocument();
  });

  it('handles mark complete functionality', async () => {
    const mockMarkComplete = vi.fn().mockResolvedValue({ data: { success: true } });
    const { useMarkLectureCompleteMutation } = require('@/redux/features/student/studentApi');
    useMarkLectureCompleteMutation.mockReturnValue([mockMarkComplete, { isLoading: false }]);

    renderWithProviders(<LecturePage />);

    await waitFor(() => {
      expect(screen.getByTestId('mark-complete-btn')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('mark-complete-btn'));

    await waitFor(() => {
      expect(mockMarkComplete).toHaveBeenCalledWith({
        studentId: 'user-123',
        courseId: 'course-123',
        lectureId: 'lecture-456'
      });
    });
  });

  it('shows completion modal when course is completed', async () => {
    // Mock completed progress
    const completedProgressData = {
      data: {
        percentage: 100,
        lectureProgress: [
          { lectureId: 'lecture-456', isCompleted: true }
        ]
      }
    };

    const { useGetCourseProgressQuery } = require('@/redux/features/student/studentApi');
    useGetCourseProgressQuery.mockReturnValue({
      data: completedProgressData,
      isLoading: false,
      error: null,
      refetch: vi.fn()
    });

    renderWithProviders(<LecturePage />);

    // Simulate marking the last lecture complete
    const mockMarkComplete = vi.fn().mockResolvedValue({ 
      data: { 
        success: true,
        courseCompleted: true 
      } 
    });
    
    const { useMarkLectureCompleteMutation } = require('@/redux/features/student/studentApi');
    useMarkLectureCompleteMutation.mockReturnValue([mockMarkComplete, { isLoading: false }]);

    await waitFor(() => {
      expect(screen.getByTestId('mark-complete-btn')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('mark-complete-btn'));

    await waitFor(() => {
      expect(screen.getByTestId('completion-modal')).toBeInTheDocument();
    });
  });

  it('handles offline state correctly', async () => {
    // Simulate offline state
    Object.defineProperty(navigator, 'onLine', { value: false });

    renderWithProviders(<LecturePage />);

    // Trigger offline event
    act(() => {
      window.dispatchEvent(new Event('offline'));
    });

    await waitFor(() => {
      expect(screen.getByText(/offline/i)).toBeInTheDocument();
    });
  });

  it('handles loading states', () => {
    const { useGetMeQuery } = require('@/redux/features/auth/authApi');
    useGetMeQuery.mockReturnValue({
      data: null,
      isLoading: true,
      error: null
    });

    renderWithProviders(<LecturePage />);

    // Should show loading state or handle gracefully
    expect(screen.getByTestId('course-layout')).toBeInTheDocument();
  });

  it('handles API errors gracefully', async () => {
    const { useGetLectureByIdQuery } = require('@/redux/features/lecture/lectureApi');
    useGetLectureByIdQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: { message: 'Failed to fetch lecture' },
      refetch: vi.fn()
    });

    renderWithProviders(<LecturePage />);

    // Should render error boundary or handle error gracefully
    await waitFor(() => {
      expect(screen.getByText(/lecture loading error/i)).toBeInTheDocument();
    });
  });

  it('handles retry mechanism', async () => {
    const mockRefetch = vi.fn();
    const { useGetLectureByIdQuery } = require('@/redux/features/lecture/lectureApi');
    
    // First call returns error
    useGetLectureByIdQuery.mockReturnValueOnce({
      data: null,
      isLoading: false,
      error: { message: 'Network error' },
      refetch: mockRefetch
    });

    renderWithProviders(<LecturePage />);

    await waitFor(() => {
      expect(screen.getByText(/retry/i)).toBeInTheDocument();
    });

    // Mock successful retry
    useGetLectureByIdQuery.mockReturnValueOnce({
      data: mockLectureData,
      isLoading: false,
      error: null,
      refetch: mockRefetch
    });

    fireEvent.click(screen.getByText(/retry/i));

    await waitFor(() => {
      expect(screen.getByTestId('course-layout')).toBeInTheDocument();
    });
  });

  it('shows performance monitoring in development', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    renderWithProviders(<LecturePage />);

    // Should show performance overlay in development
    expect(screen.getByText(/performance:/i)).toBeInTheDocument();
    expect(screen.getByText(/cache:/i)).toBeInTheDocument();
    expect(screen.getByText(/status:/i)).toBeInTheDocument();

    process.env.NODE_ENV = originalEnv;
  });

  it('handles navigation back on error', async () => {
    const mockNavigate = vi.fn();
    vi.doMock('react-router-dom', async () => {
      const actual = await vi.importActual('react-router-dom');
      return {
        ...actual,
        useNavigate: () => mockNavigate,
        useParams: () => ({
          courseId: 'course-123',
          lectureId: 'lecture-456'
        })
      };
    });

    const { useGetLectureByIdQuery } = require('@/redux/features/lecture/lectureApi');
    useGetLectureByIdQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: { message: 'Lecture not found' },
      refetch: vi.fn()
    });

    renderWithProviders(<LecturePage />);

    await waitFor(() => {
      expect(screen.getByText('Go Back')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Go Back'));

    expect(mockNavigate).toHaveBeenCalledWith('/student/course/course-123');
  });

  it('caches data for offline access', async () => {
    renderWithProviders(<LecturePage />);

    await waitFor(() => {
      expect(screen.getByTestId('course-layout')).toBeInTheDocument();
    });

    // Simulate going offline
    act(() => {
      navigator.onLine = false;
      window.dispatchEvent(new Event('offline'));
    });

    // Should still work with cached data
    expect(screen.getByTestId('course-layout')).toBeInTheDocument();
  });

  it('handles enrollment check', async () => {
    // Mock user not enrolled
    const { useGetEnrolledCoursesQuery } = require('@/redux/features/student/studentApi');
    useGetEnrolledCoursesQuery.mockReturnValue({
      data: { data: [] }, // Empty enrollment
      isLoading: false,
      error: null
    });

    renderWithProviders(<LecturePage />);

    // Should handle non-enrolled state appropriately
    await waitFor(() => {
      expect(screen.getByTestId('course-layout')).toBeInTheDocument();
    });
  });
});
