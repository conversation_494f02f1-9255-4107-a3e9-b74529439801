import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import { Provider } from 'react-redux';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import Courses from '../Courses';
import { courseManagementSlice } from '@/redux/features/course/courseManagementSlice';

// Mock the API hooks
vi.mock('@/redux/features/auth/authApi', () => ({
  useGetMeQuery: () => ({
    data: { data: { _id: 'teacher1' } },
    isLoading: false
  })
}));

vi.mock('@/redux/features/course/courseApi', () => ({
  useGetCreatorCourseQuery: () => ({
    data: {
      data: [
        {
          _id: '1',
          title: 'React Fundamentals',
          subtitle: 'Learn React from scratch',
          description: 'A comprehensive React course',
          category: 'Programming',
          courseLevel: 'Beginner',
          coursePrice: 99,
          courseThumbnail: 'https://example.com/thumb1.jpg',
          enrolledStudents: ['student1', 'student2'],
          totalEnrollment: 2,
          lectures: [],
          creator: 'teacher1',
          isPublished: true,
          status: 'published',
          isFree: 'paid',
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-15')
        },
        {
          _id: '2',
          title: 'Advanced JavaScript',
          subtitle: 'Master JavaScript concepts',
          description: 'Deep dive into JavaScript',
          category: 'Programming',
          courseLevel: 'Advanced',
          coursePrice: 0,
          courseThumbnail: 'https://example.com/thumb2.jpg',
          enrolledStudents: ['student3'],
          totalEnrollment: 1,
          lectures: [],
          creator: 'teacher1',
          isPublished: false,
          status: 'draft',
          isFree: 'free',
          createdAt: new Date('2024-01-10'),
          updatedAt: new Date('2024-01-20')
        }
      ]
    },
    isLoading: false,
    error: null,
    refetch: vi.fn()
  })
}));

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  }
}));

// Mock components to simplify testing
vi.mock('@/components/Courses/CourseStatsOverview', () => ({
  default: ({ stats, isLoading }: any) => (
    <div data-testid="course-stats-overview">
      {isLoading ? 'Loading stats...' : `Total: ${stats.totalCourses}`}
    </div>
  )
}));

vi.mock('@/components/Courses/CourseDataTable', () => ({
  default: ({ courses, isLoading }: any) => (
    <div data-testid="course-data-table">
      {isLoading ? 'Loading table...' : `Courses: ${courses.length}`}
    </div>
  )
}));

vi.mock('@/components/Courses/CourseFilters', () => ({
  default: ({ filters, onFiltersChange }: any) => (
    <div data-testid="course-filters">
      <button onClick={() => onFiltersChange({ search: 'test' })}>
        Apply Filter
      </button>
    </div>
  )
}));

vi.mock('@/components/Courses/CourseSearch', () => ({
  default: ({ value, onChange }: any) => (
    <input
      data-testid="course-search"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder="Search courses..."
    />
  )
}));

const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      courseManagement: courseManagementSlice.reducer
    },
    preloadedState: {
      courseManagement: {
        filters: {
          search: '',
          status: [],
          category: [],
          level: [],
          priceRange: { min: 0, max: 10000 },
          isFree: undefined,
          dateRange: {},
          enrollmentRange: { min: 0, max: 10000 },
          ratingRange: { min: 0, max: 5 },
          sortBy: 'updatedAt',
          sortOrder: 'desc',
          page: 1,
          limit: 20,
          tags: [],
          instructor: '',
          language: '',
          duration: { min: 0, max: 1000 }
        },
        viewMode: 'table',
        ui: {
          activeTab: 'overview',
          sidebarCollapsed: false,
          showFilters: false
        },
        selectedCourses: [],
        tableColumns: [
          { key: 'title', label: 'Title', sortable: true, visible: true, width: 200 },
          { key: 'status', label: 'Status', sortable: true, visible: true, width: 100 },
          { key: 'enrollments', label: 'Students', sortable: true, visible: true, width: 100 },
          { key: 'price', label: 'Price', sortable: true, visible: true, width: 100 },
          { key: 'updatedAt', label: 'Updated', sortable: true, visible: true, width: 120 }
        ],
        ...initialState
      }
    }
  });
};

const renderWithProviders = (component: React.ReactElement, initialState = {}) => {
  const store = createTestStore(initialState);
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('Courses Page', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the courses page correctly', () => {
    renderWithProviders(<Courses />);
    
    expect(screen.getByText('Courses')).toBeInTheDocument();
    expect(screen.getByText('Manage and analyze your course portfolio')).toBeInTheDocument();
  });

  it('displays header with action buttons', () => {
    renderWithProviders(<Courses />);
    
    expect(screen.getByText('Refresh')).toBeInTheDocument();
    expect(screen.getByText('Export')).toBeInTheDocument();
    expect(screen.getByText('Create Course')).toBeInTheDocument();
  });

  it('shows tabs navigation', () => {
    renderWithProviders(<Courses />);
    
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Courses')).toBeInTheDocument();
    expect(screen.getByText('Analytics')).toBeInTheDocument();
    expect(screen.getByText('Insights')).toBeInTheDocument();
  });

  it('displays overview tab by default', () => {
    renderWithProviders(<Courses />);
    
    expect(screen.getByTestId('course-stats-overview')).toBeInTheDocument();
    expect(screen.getByText('Total: 2')).toBeInTheDocument();
  });

  it('switches to courses tab', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Courses />);
    
    const coursesTab = screen.getByText('Courses');
    await user.click(coursesTab);
    
    expect(screen.getByTestId('course-data-table')).toBeInTheDocument();
    expect(screen.getByTestId('course-filters')).toBeInTheDocument();
    expect(screen.getByTestId('course-search')).toBeInTheDocument();
  });

  it('handles search functionality', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Courses />);
    
    // Switch to courses tab
    await user.click(screen.getByText('Courses'));
    
    const searchInput = screen.getByTestId('course-search');
    await user.type(searchInput, 'React');
    
    expect(searchInput).toHaveValue('React');
  });

  it('handles filter changes', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Courses />);
    
    // Switch to courses tab
    await user.click(screen.getByText('Courses'));
    
    const applyFilterButton = screen.getByText('Apply Filter');
    await user.click(applyFilterButton);
    
    // Filter should be applied (mocked component behavior)
    expect(screen.getByTestId('course-filters')).toBeInTheDocument();
  });

  it('handles refresh action', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Courses />);
    
    const refreshButton = screen.getByText('Refresh');
    await user.click(refreshButton);
    
    // Should show loading state briefly
    expect(refreshButton).toBeInTheDocument();
  });

  it('handles export action', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Courses />);
    
    const exportButton = screen.getByText('Export');
    await user.click(exportButton);
    
    // Export functionality (mocked)
    expect(exportButton).toBeInTheDocument();
  });

  it('displays view mode toggles in courses tab', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Courses />);
    
    // Switch to courses tab
    await user.click(screen.getByText('Courses'));
    
    // View mode buttons should be present
    expect(screen.getByRole('button', { name: /table/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /grid/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /list/i })).toBeInTheDocument();
  });

  it('shows analytics placeholder', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Courses />);
    
    const analyticsTab = screen.getByText('Analytics');
    await user.click(analyticsTab);
    
    expect(screen.getByText('Advanced Analytics')).toBeInTheDocument();
    expect(screen.getByText('Detailed analytics features coming soon')).toBeInTheDocument();
  });

  it('shows insights placeholder', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Courses />);
    
    const insightsTab = screen.getByText('Insights');
    await user.click(insightsTab);
    
    expect(screen.getByText('AI-Powered Insights')).toBeInTheDocument();
    expect(screen.getByText('Smart insights and recommendations coming soon')).toBeInTheDocument();
  });

  it('handles loading state', () => {
    // Mock loading state
    vi.mocked(require('@/redux/features/course/courseApi').useGetCreatorCourseQuery).mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
      refetch: vi.fn()
    });

    renderWithProviders(<Courses />);
    
    // Should show loading skeleton or state
    expect(screen.getByText('Loading stats...')).toBeInTheDocument();
  });

  it('handles error state', () => {
    // Mock error state
    vi.mocked(require('@/redux/features/course/courseApi').useGetCreatorCourseQuery).mockReturnValue({
      data: null,
      isLoading: false,
      error: { message: 'Failed to load courses' },
      refetch: vi.fn()
    });

    renderWithProviders(<Courses />);
    
    // Error should be handled gracefully
    expect(screen.getByText('Courses')).toBeInTheDocument();
  });

  it('handles missing teacher ID', () => {
    // Mock missing user data
    vi.mocked(require('@/redux/features/auth/authApi').useGetMeQuery).mockReturnValue({
      data: null,
      isLoading: false
    });

    renderWithProviders(<Courses />);
    
    // Should show loading state for missing teacher
    expect(screen.getByRole('main')).toBeInTheDocument();
  });

  it('maintains state across tab switches', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Courses />);
    
    // Switch to courses tab and apply search
    await user.click(screen.getByText('Courses'));
    const searchInput = screen.getByTestId('course-search');
    await user.type(searchInput, 'React');
    
    // Switch to overview and back
    await user.click(screen.getByText('Overview'));
    await user.click(screen.getByText('Courses'));
    
    // Search should be maintained
    expect(screen.getByTestId('course-search')).toHaveValue('React');
  });

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup();
    renderWithProviders(<Courses />);
    
    // Tab navigation
    await user.tab();
    expect(document.activeElement).toHaveAttribute('type', 'button');
    
    // Enter key should work on focused elements
    await user.keyboard('{Enter}');
  });

  it('is accessible', () => {
    renderWithProviders(<Courses />);
    
    // Check for proper ARIA labels and roles
    expect(screen.getByRole('main')).toHaveAttribute('aria-label', 'Courses dashboard');
    expect(screen.getByRole('tablist')).toBeInTheDocument();
  });
});
