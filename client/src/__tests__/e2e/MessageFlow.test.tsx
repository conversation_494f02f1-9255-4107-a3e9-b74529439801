import React from 'react';
import { screen, waitFor, fireEvent, within } from '@testing-library/react';
import { render, createMockUser, createMockThread, createMockMessage, mockApiResponse, mockPaginatedResponse, setupTests, cleanupTests, userEvent } from '@/test-utils';
import { MessageLayout } from '@/components/Messages';
import { FolderType, MessageType, MessagePriority } from '@/types/message';

// Mock all the APIs
jest.mock('@/redux/features/message/messageApi', () => ({
  useGetMessageFoldersQuery: jest.fn(),
  useGetMessagesByFolderQuery: jest.fn(),
  useGetThreadMessagesQuery: jest.fn(),
  useSendMessageMutation: jest.fn(),
  useReplyToMessageMutation: jest.fn(),
  useMarkThreadAsReadMutation: jest.fn(),
  useToggleMessageStarMutation: jest.fn(),
  useToggleThreadArchiveMutation: jest.fn(),
  useDeleteMessagesMutation: jest.fn(),
  useSearchMessagesQuery: jest.fn(),
  useGetMessageStatsQuery: jest.fn(),
  useSaveDraftMutation: jest.fn(),
  useGetDraftsQuery: jest.fn(),
  useDeleteDraftMutation: jest.fn(),
}));

jest.mock('@/redux/features/auth/authApi', () => ({
  useGetMeQuery: jest.fn(),
}));

jest.mock('@/redux/hooks', () => ({
  useAppSelector: jest.fn(),
  useAppDispatch: jest.fn(),
}));

const mockMessageApi = require('@/redux/features/message/messageApi');
const mockAuthApi = require('@/redux/features/auth/authApi');
const mockReduxHooks = require('@/redux/hooks');

describe('Message Flow E2E Tests', () => {
  const mockUser = createMockUser();
  const mockDispatch = jest.fn();
  
  const mockThreads = [
    createMockThread({
      _id: 'thread-1',
      subject: 'Important Course Update',
      unreadCount: 2,
      participants: [
        mockUser,
        createMockUser({ _id: 'student-1', name: { firstName: 'Alice', lastName: 'Johnson' } })
      ]
    }),
    createMockThread({
      _id: 'thread-2',
      subject: 'Assignment Question',
      unreadCount: 0,
      participants: [
        mockUser,
        createMockUser({ _id: 'student-2', name: { firstName: 'Bob', lastName: 'Smith' } })
      ]
    }),
  ];

  const mockMessages = [
    createMockMessage({
      _id: 'message-1',
      threadId: 'thread-1',
      content: 'Hello, I have a question about the assignment.',
      sender: createMockUser({ _id: 'student-1', name: { firstName: 'Alice', lastName: 'Johnson' } }),
      recipient: mockUser,
    }),
    createMockMessage({
      _id: 'message-2',
      threadId: 'thread-1',
      content: 'Sure, what would you like to know?',
      sender: mockUser,
      recipient: createMockUser({ _id: 'student-1' }),
    }),
  ];

  const mockFolders = [
    {
      _id: 'inbox',
      name: 'Inbox',
      type: FolderType.INBOX,
      messageCount: 10,
      unreadCount: 3,
      isDefault: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];

  const mockStats = {
    totalMessages: 25,
    unreadMessages: 3,
    sentMessages: 15,
    receivedMessages: 10,
    starredMessages: 5,
    archivedMessages: 2,
    messagesByType: {},
    messagesByPriority: {},
    responseTime: { average: 120, median: 90 },
    activityTrends: [],
  };

  beforeEach(() => {
    setupTests();
    
    // Mock Redux state
    mockReduxHooks.useAppDispatch.mockReturnValue(mockDispatch);
    mockReduxHooks.useAppSelector.mockReturnValue({
      currentFolder: FolderType.INBOX,
      filters: {},
      searchQuery: '',
      selectedThreadId: undefined,
      selectedMessageIds: [],
      isComposing: false,
      sidebarCollapsed: false,
      previewPaneVisible: true,
    });

    // Mock auth API
    mockAuthApi.useGetMeQuery.mockReturnValue({
      data: { data: mockUser },
    });

    // Mock message APIs
    mockMessageApi.useGetMessageFoldersQuery.mockReturnValue({
      data: mockApiResponse(mockFolders),
      isLoading: false,
      error: null,
    });

    mockMessageApi.useGetMessagesByFolderQuery.mockReturnValue({
      data: mockApiResponse(mockPaginatedResponse(mockThreads)),
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    });

    mockMessageApi.useGetThreadMessagesQuery.mockReturnValue({
      data: mockApiResponse(mockPaginatedResponse(mockMessages)),
      isLoading: false,
      error: null,
    });

    mockMessageApi.useGetMessageStatsQuery.mockReturnValue({
      data: mockApiResponse(mockStats),
      isLoading: false,
      error: null,
    });

    // Mock mutations
    mockMessageApi.useSendMessageMutation.mockReturnValue([
      jest.fn().mockResolvedValue({ unwrap: () => Promise.resolve({}) }),
      { isLoading: false },
    ]);

    mockMessageApi.useReplyToMessageMutation.mockReturnValue([
      jest.fn().mockResolvedValue({ unwrap: () => Promise.resolve({}) }),
      { isLoading: false },
    ]);

    mockMessageApi.useMarkThreadAsReadMutation.mockReturnValue([
      jest.fn(),
      { isLoading: false },
    ]);

    mockMessageApi.useSaveDraftMutation.mockReturnValue([
      jest.fn().mockResolvedValue({ unwrap: () => Promise.resolve({}) }),
      { isLoading: false },
    ]);
  });

  afterEach(() => {
    cleanupTests();
    jest.clearAllMocks();
  });

  it('should display message layout with sidebar and message list', async () => {
    render(<MessageLayout />);

    await waitFor(() => {
      // Should show sidebar with folders
      expect(screen.getByText('Compose')).toBeInTheDocument();
      expect(screen.getByText('Inbox')).toBeInTheDocument();
      
      // Should show message list
      expect(screen.getByText('Important Course Update')).toBeInTheDocument();
      expect(screen.getByText('Assignment Question')).toBeInTheDocument();
      
      // Should show unread count
      expect(screen.getByText('2')).toBeInTheDocument(); // unread count badge
    });
  });

  it('should allow user to select and view a message thread', async () => {
    const user = userEvent.setup();
    
    // Mock thread selection
    mockReduxHooks.useAppSelector.mockReturnValue({
      currentFolder: FolderType.INBOX,
      filters: {},
      searchQuery: '',
      selectedThreadId: 'thread-1',
      selectedMessageIds: [],
      isComposing: false,
      sidebarCollapsed: false,
      previewPaneVisible: true,
    });

    render(<MessageLayout />);

    await waitFor(() => {
      // Click on first thread
      const threadElement = screen.getByText('Important Course Update');
      user.click(threadElement);
    });

    await waitFor(() => {
      // Should show thread messages
      expect(screen.getByText('Hello, I have a question about the assignment.')).toBeInTheDocument();
      expect(screen.getByText('Sure, what would you like to know?')).toBeInTheDocument();
    });
  });

  it('should allow user to compose a new message', async () => {
    const user = userEvent.setup();
    
    // Mock composing state
    mockReduxHooks.useAppSelector.mockReturnValue({
      currentFolder: FolderType.INBOX,
      filters: {},
      searchQuery: '',
      selectedThreadId: undefined,
      selectedMessageIds: [],
      isComposing: true,
      composeDraft: undefined,
      sidebarCollapsed: false,
      previewPaneVisible: true,
    });

    render(<MessageLayout />);

    await waitFor(() => {
      // Should show compose dialog
      expect(screen.getByText('Compose Message')).toBeInTheDocument();
      expect(screen.getByLabelText('To')).toBeInTheDocument();
      expect(screen.getByLabelText('Subject')).toBeInTheDocument();
      expect(screen.getByLabelText('Message')).toBeInTheDocument();
    });
  });

  it('should allow user to reply to a message', async () => {
    const user = userEvent.setup();
    const mockReplyMutation = jest.fn().mockResolvedValue({ unwrap: () => Promise.resolve({}) });
    
    mockMessageApi.useReplyToMessageMutation.mockReturnValue([
      mockReplyMutation,
      { isLoading: false },
    ]);

    // Mock thread selection
    mockReduxHooks.useAppSelector.mockReturnValue({
      currentFolder: FolderType.INBOX,
      filters: {},
      searchQuery: '',
      selectedThreadId: 'thread-1',
      selectedMessageIds: [],
      isComposing: false,
      sidebarCollapsed: false,
      previewPaneVisible: true,
    });

    render(<MessageLayout />);

    await waitFor(() => {
      // Should show reply textarea
      const replyTextarea = screen.getByPlaceholderText('Type your reply...');
      expect(replyTextarea).toBeInTheDocument();
    });

    // Type a reply
    const replyTextarea = screen.getByPlaceholderText('Type your reply...');
    await user.type(replyTextarea, 'This is my reply');

    // Click send button
    const sendButton = screen.getByText('Send');
    await user.click(sendButton);

    await waitFor(() => {
      expect(mockReplyMutation).toHaveBeenCalledWith({
        threadId: 'thread-1',
        content: 'This is my reply',
      });
    });
  });

  it('should allow user to search messages', async () => {
    const user = userEvent.setup();
    
    mockMessageApi.useSearchMessagesQuery.mockReturnValue({
      data: mockApiResponse(mockPaginatedResponse([])),
      isLoading: false,
      error: null,
    });

    render(<MessageLayout />);

    await waitFor(() => {
      // Find and click search button
      const searchButton = screen.getByTitle('Search messages (Ctrl+K)');
      user.click(searchButton);
    });

    // Note: In a real E2E test, you would interact with the search modal
    // For this unit test, we're just verifying the search button exists
    expect(screen.getByTitle('Search messages (Ctrl+K)')).toBeInTheDocument();
  });

  it('should handle bulk message operations', async () => {
    const user = userEvent.setup();
    const mockDeleteMutation = jest.fn();
    
    mockMessageApi.useDeleteMessagesMutation.mockReturnValue([
      mockDeleteMutation,
      { isLoading: false },
    ]);

    render(<MessageLayout />);

    await waitFor(() => {
      // Select all checkbox
      const selectAllCheckbox = screen.getAllByRole('checkbox')[0];
      user.click(selectAllCheckbox);
    });

    // Note: In a real E2E test, you would verify bulk actions appear
    // and test their functionality
  });

  it('should show message statistics in sidebar', async () => {
    render(<MessageLayout />);

    await waitFor(() => {
      // Should show total messages
      expect(screen.getByText('Total Messages:')).toBeInTheDocument();
      expect(screen.getByText('25')).toBeInTheDocument();
      
      // Should show unread count
      expect(screen.getByText('Unread:')).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument();
    });
  });

  it('should handle responsive layout on mobile', async () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500,
    });

    render(<MessageLayout />);

    await waitFor(() => {
      // Should show mobile header
      expect(screen.getByText('Messages')).toBeInTheDocument();
      
      // Should show hamburger menu button
      const menuButton = screen.getByRole('button');
      expect(menuButton).toBeInTheDocument();
    });
  });

  it('should handle keyboard navigation', async () => {
    render(<MessageLayout />);

    await waitFor(() => {
      // Test keyboard shortcuts (simplified)
      const messageList = screen.getByText('Important Course Update').closest('div');
      
      if (messageList) {
        // Simulate Ctrl+K for search
        fireEvent.keyDown(messageList, {
          key: 'k',
          ctrlKey: true,
        });
        
        // In a real test, you would verify search modal opens
      }
    });
  });
});
