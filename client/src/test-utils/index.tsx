import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import { baseApi } from '@/redux/api/baseApi';
import authReducer from '@/redux/features/auth/authSlice';
import messageReducer from '@/redux/features/message/messageSlice';

// Mock store configuration for testing
const createTestStore = (preloadedState?: any) => {
  return configureStore({
    reducer: {
      [baseApi.reducerPath]: baseApi.reducer,
      auth: authReducer,
      message: messageReducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: [baseApi.util.resetApiState.type],
        },
      }).concat(baseApi.middleware),
    preloadedState,
  });
};

// Test wrapper component
interface AllTheProvidersProps {
  children: React.ReactNode;
  initialState?: any;
}

const AllTheProviders: React.FC<AllTheProvidersProps> = ({ 
  children, 
  initialState 
}) => {
  const store = createTestStore(initialState);
  
  return (
    <Provider store={store}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </Provider>
  );
};

// Custom render function
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'> & {
    initialState?: any;
  }
) => {
  const { initialState, ...renderOptions } = options || {};
  
  return render(ui, {
    wrapper: (props) => <AllTheProviders {...props} initialState={initialState} />,
    ...renderOptions,
  });
};

// Mock data generators
export const createMockUser = (overrides = {}) => ({
  _id: 'user-123',
  name: {
    firstName: 'John',
    lastName: 'Doe',
  },
  email: '<EMAIL>',
  role: 'teacher' as const,
  profileImg: '',
  photoUrl: '',
  ...overrides,
});

export const createMockMessage = (overrides = {}) => ({
  _id: 'message-123',
  threadId: 'thread-123',
  sender: createMockUser(),
  recipient: createMockUser({ _id: 'user-456', email: '<EMAIL>' }),
  subject: 'Test Message',
  content: 'This is a test message content',
  attachments: [],
  messageType: 'direct' as const,
  priority: 'normal' as const,
  status: 'sent' as const,
  isRead: false,
  isStarred: false,
  isArchived: false,
  isDeleted: false,
  metadata: {},
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const createMockThread = (overrides = {}) => ({
  _id: 'thread-123',
  participants: [createMockUser(), createMockUser({ _id: 'user-456' })],
  subject: 'Test Thread',
  lastMessage: createMockMessage(),
  messageCount: 5,
  unreadCount: 2,
  isArchived: false,
  isPinned: false,
  tags: [],
  metadata: {},
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const createMockAnalyticsData = (overrides = {}) => ({
  overview: {
    totalStudents: 1234,
    totalRevenue: 45231,
    totalCourses: 24,
    averageRating: 4.7,
  },
  ...overrides,
});

// Mock API responses
export const mockApiResponse = <T>(data: T, success = true) => ({
  success,
  message: success ? 'Success' : 'Error',
  data,
});

export const mockPaginatedResponse = <T>(data: T[], page = 1, limit = 20) => ({
  data,
  pagination: {
    page,
    limit,
    total: data.length,
    totalPages: Math.ceil(data.length / limit),
    hasNext: page * limit < data.length,
    hasPrev: page > 1,
  },
});

// Mock localStorage
export const mockLocalStorage = () => {
  const store: Record<string, string> = {};
  
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      Object.keys(store).forEach(key => delete store[key]);
    }),
  };
};

// Mock IntersectionObserver
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = jest.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  });
  window.IntersectionObserver = mockIntersectionObserver;
};

// Mock ResizeObserver
export const mockResizeObserver = () => {
  const mockResizeObserver = jest.fn();
  mockResizeObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  });
  window.ResizeObserver = mockResizeObserver;
};

// Mock matchMedia
export const mockMatchMedia = (matches = false) => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });
};

// Setup function for tests
export const setupTests = () => {
  mockIntersectionObserver();
  mockResizeObserver();
  mockMatchMedia();
  
  // Mock console methods to reduce noise in tests
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
  
  // Mock window.URL.createObjectURL
  window.URL.createObjectURL = jest.fn(() => 'mock-url');
  window.URL.revokeObjectURL = jest.fn();
};

// Cleanup function for tests
export const cleanupTests = () => {
  jest.restoreAllMocks();
};

// Re-export everything from React Testing Library
export * from '@testing-library/react';
export { customRender as render };

// Export user event
export { default as userEvent } from '@testing-library/user-event';
