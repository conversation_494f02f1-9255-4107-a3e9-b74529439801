import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore, Store } from '@reduxjs/toolkit';
import { vi } from 'vitest';
import { courseManagementSlice } from '@/redux/features/course/courseManagementSlice';
import { EnhancedCourse, CourseFilters } from '@/types/course-management';

// Mock course data
export const mockCourses: EnhancedCourse[] = [
  {
    _id: '1',
    title: 'React Fundamentals',
    subtitle: 'Learn React from scratch',
    description: 'A comprehensive React course for beginners',
    category: 'Programming',
    courseLevel: 'Beginner',
    coursePrice: 99,
    courseThumbnail: 'https://example.com/thumb1.jpg',
    enrolledStudents: ['student1', 'student2'],
    totalEnrollment: 2,
    lectures: [],
    creator: 'teacher1',
    isPublished: true,
    status: 'published',
    isFree: 'paid',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15'),
    totalRevenue: 198,
    averageRating: 4.5,
    completionRate: 85,
    enrollmentTrend: 'up',
    analytics: {
      enrollmentCount: 2,
      completionRate: 85,
      averageRating: 4.5,
      totalRevenue: 198,
      monthlyEnrollments: 2,
      weeklyEnrollments: 1,
      enrollmentGrowth: 15,
      ratingTrend: 5,
      revenueTrend: 20,
      engagementScore: 90,
      dropoffRate: 10,
      averageWatchTime: 45,
      certificatesIssued: 1,
      studentSatisfaction: 95
    }
  },
  {
    _id: '2',
    title: 'Advanced JavaScript',
    subtitle: 'Master JavaScript concepts',
    description: 'Deep dive into advanced JavaScript topics',
    category: 'Programming',
    courseLevel: 'Advanced',
    coursePrice: 149,
    courseThumbnail: 'https://example.com/thumb2.jpg',
    enrolledStudents: ['student3', 'student4', 'student5'],
    totalEnrollment: 3,
    lectures: [],
    creator: 'teacher1',
    isPublished: true,
    status: 'published',
    isFree: 'paid',
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-20'),
    totalRevenue: 447,
    averageRating: 4.8,
    completionRate: 92,
    enrollmentTrend: 'up',
    analytics: {
      enrollmentCount: 3,
      completionRate: 92,
      averageRating: 4.8,
      totalRevenue: 447,
      monthlyEnrollments: 3,
      weeklyEnrollments: 2,
      enrollmentGrowth: 25,
      ratingTrend: 8,
      revenueTrend: 35,
      engagementScore: 95,
      dropoffRate: 5,
      averageWatchTime: 52,
      certificatesIssued: 2,
      studentSatisfaction: 98
    }
  },
  {
    _id: '3',
    title: 'Free CSS Course',
    subtitle: 'Learn CSS basics',
    description: 'A free course covering CSS fundamentals',
    category: 'Design',
    courseLevel: 'Beginner',
    coursePrice: 0,
    courseThumbnail: 'https://example.com/thumb3.jpg',
    enrolledStudents: ['student6'],
    totalEnrollment: 1,
    lectures: [],
    creator: 'teacher1',
    isPublished: false,
    status: 'draft',
    isFree: 'free',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-25'),
    totalRevenue: 0,
    averageRating: 0,
    completionRate: 0,
    enrollmentTrend: 'stable',
    analytics: {
      enrollmentCount: 1,
      completionRate: 0,
      averageRating: 0,
      totalRevenue: 0,
      monthlyEnrollments: 1,
      weeklyEnrollments: 0,
      enrollmentGrowth: 0,
      ratingTrend: 0,
      revenueTrend: 0,
      engagementScore: 0,
      dropoffRate: 0,
      averageWatchTime: 0,
      certificatesIssued: 0,
      studentSatisfaction: 0
    }
  }
];

// Default filters
export const defaultFilters: CourseFilters = {
  search: '',
  status: [],
  category: [],
  level: [],
  priceRange: { min: 0, max: 10000 },
  isFree: undefined,
  dateRange: {},
  enrollmentRange: { min: 0, max: 10000 },
  ratingRange: { min: 0, max: 5 },
  sortBy: 'updatedAt',
  sortOrder: 'desc',
  page: 1,
  limit: 20,
  tags: [],
  instructor: '',
  language: '',
  duration: { min: 0, max: 1000 }
};

// Default table columns
export const defaultTableColumns = [
  { key: 'title', label: 'Title', sortable: true, visible: true, width: 200, type: 'text' },
  { key: 'status', label: 'Status', sortable: true, visible: true, width: 100, type: 'status' },
  { key: 'enrollments', label: 'Students', sortable: true, visible: true, width: 100, type: 'number' },
  { key: 'price', label: 'Price', sortable: true, visible: true, width: 100, type: 'currency' },
  { key: 'revenue', label: 'Revenue', sortable: true, visible: true, width: 120, type: 'currency' },
  { key: 'rating', label: 'Rating', sortable: true, visible: true, width: 100, type: 'rating' },
  { key: 'updatedAt', label: 'Updated', sortable: true, visible: true, width: 120, type: 'date' }
];

// Create test store
export const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      courseManagement: courseManagementSlice.reducer
    },
    preloadedState: {
      courseManagement: {
        filters: defaultFilters,
        viewMode: 'table',
        ui: {
          activeTab: 'overview',
          sidebarCollapsed: false,
          showFilters: false
        },
        selectedCourses: [],
        tableColumns: defaultTableColumns,
        ...initialState
      }
    }
  });
};

// Custom render function
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialState?: any;
  store?: Store;
}

export const renderWithProviders = (
  ui: React.ReactElement,
  {
    initialState = {},
    store = createTestStore(initialState),
    ...renderOptions
  }: CustomRenderOptions = {}
) => {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <Provider store={store}>
        <BrowserRouter>
          {children}
        </BrowserRouter>
      </Provider>
    );
  }

  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) };
};

// Mock API responses
export const mockApiResponses = {
  getUserSuccess: {
    data: { data: { _id: 'teacher1', name: 'Test Teacher', email: '<EMAIL>' } },
    isLoading: false,
    error: null
  },
  getCoursesSuccess: {
    data: { data: mockCourses },
    isLoading: false,
    error: null,
    refetch: vi.fn()
  },
  getCoursesLoading: {
    data: null,
    isLoading: true,
    error: null,
    refetch: vi.fn()
  },
  getCoursesError: {
    data: null,
    isLoading: false,
    error: { message: 'Failed to load courses' },
    refetch: vi.fn()
  }
};

// Mock functions
export const createMockHandlers = () => ({
  onFiltersChange: vi.fn(),
  onSelectionChange: vi.fn(),
  onSort: vi.fn(),
  onColumnResize: vi.fn(),
  onColumnToggle: vi.fn(),
  onRowClick: vi.fn(),
  onBulkAction: vi.fn(),
  onEdit: vi.fn(),
  onDuplicate: vi.fn(),
  onDelete: vi.fn(),
  onViewAnalytics: vi.fn(),
  onCreateNew: vi.fn(),
  onSaveFilter: vi.fn(),
  onLoadFilter: vi.fn()
});

// Test utilities for accessibility
export const axeMatchers = {
  toHaveNoViolations: expect.extend({
    async toHaveNoViolations(received) {
      // This would integrate with axe-core for accessibility testing
      // For now, it's a placeholder
      return {
        pass: true,
        message: () => 'No accessibility violations found'
      };
    }
  })
};

// Mock intersection observer for lazy loading tests
export const mockIntersectionObserver = () => {
  const mockIntersectionObserver = vi.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  });
  window.IntersectionObserver = mockIntersectionObserver;
  return mockIntersectionObserver;
};

// Mock resize observer for responsive tests
export const mockResizeObserver = () => {
  const mockResizeObserver = vi.fn();
  mockResizeObserver.mockReturnValue({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  });
  window.ResizeObserver = mockResizeObserver;
  return mockResizeObserver;
};

// Mock media query for responsive tests
export const mockMediaQuery = (matches: boolean) => {
  const mockMatchMedia = vi.fn().mockImplementation((query) => ({
    matches,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }));
  window.matchMedia = mockMatchMedia;
  return mockMatchMedia;
};

// Performance testing utilities
export const measureRenderTime = async (renderFn: () => void) => {
  const start = performance.now();
  renderFn();
  const end = performance.now();
  return end - start;
};

// Accessibility testing utilities
export const getAccessibilityTree = (container: HTMLElement) => {
  const elements = container.querySelectorAll('*');
  const accessibilityInfo = Array.from(elements).map(element => ({
    tagName: element.tagName,
    role: element.getAttribute('role'),
    ariaLabel: element.getAttribute('aria-label'),
    ariaLabelledBy: element.getAttribute('aria-labelledby'),
    ariaDescribedBy: element.getAttribute('aria-describedby'),
    tabIndex: element.getAttribute('tabindex')
  }));
  return accessibilityInfo;
};

// Custom matchers for testing
export const customMatchers = {
  toBeAccessible: (received: HTMLElement) => {
    const accessibilityTree = getAccessibilityTree(received);
    const hasProperLabels = accessibilityTree.every(element => 
      element.ariaLabel || element.ariaLabelledBy || element.tagName === 'DIV'
    );
    
    return {
      pass: hasProperLabels,
      message: () => hasProperLabels 
        ? 'Element is accessible'
        : 'Element lacks proper accessibility labels'
    };
  }
};

// Export everything for easy importing
export * from '@testing-library/react';
export { vi } from 'vitest';
export { default as userEvent } from '@testing-library/user-event';
