import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import { render, createMockUser, createMockAnalyticsData, mockApiResponse, setupTests, cleanupTests } from '@/test-utils';
import AnalyticsOverview from '../AnalyticsOverview';
import { AnalyticsFilters } from '@/types/analytics';

// Mock the API
jest.mock('@/redux/features/analytics/analyticsApi', () => ({
  useGetDashboardSummaryQuery: jest.fn(),
  useGetRecentActivitiesQuery: jest.fn(),
  useGetAnalyticsInsightsQuery: jest.fn(),
}));

jest.mock('@/redux/features/auth/authApi', () => ({
  useGetMeQuery: jest.fn(),
}));

const mockUseGetDashboardSummaryQuery = require('@/redux/features/analytics/analyticsApi').useGetDashboardSummaryQuery;
const mockUseGetRecentActivitiesQuery = require('@/redux/features/analytics/analyticsApi').useGetRecentActivitiesQuery;
const mockUseGetAnalyticsInsightsQuery = require('@/redux/features/analytics/analyticsApi').useGetAnalyticsInsightsQuery;
const mockUseGetMeQuery = require('@/redux/features/auth/authApi').useGetMeQuery;

describe('AnalyticsOverview Component', () => {
  const mockFilters: AnalyticsFilters = {
    period: 'monthly',
    startDate: '2024-01-01',
    endDate: '2024-01-31',
  };

  const mockUser = createMockUser();
  const mockDashboardData = createMockAnalyticsData();
  const mockActivities = [
    {
      _id: 'activity-1',
      type: 'enrollment',
      title: 'New Student Enrollment',
      description: 'John Smith enrolled in React Fundamentals',
      priority: 'normal',
      createdAt: new Date().toISOString(),
    },
    {
      _id: 'activity-2',
      type: 'course_published',
      title: 'Course Published',
      description: 'Advanced JavaScript course has been published',
      priority: 'high',
      createdAt: new Date().toISOString(),
    },
  ];

  const mockInsights = [
    {
      id: 'insight-1',
      type: 'opportunity',
      title: 'Increase Course Engagement',
      description: 'Students are spending less time on video content',
      impact: 'high',
      confidence: 85,
      actionable: true,
      relatedMetrics: ['engagement'],
      generatedAt: new Date().toISOString(),
    },
    {
      id: 'insight-2',
      type: 'achievement',
      title: 'High Completion Rate',
      description: 'Your React course has 95% completion rate',
      impact: 'medium',
      confidence: 92,
      actionable: false,
      relatedMetrics: ['completion'],
      generatedAt: new Date().toISOString(),
    },
  ];

  beforeEach(() => {
    setupTests();
    
    mockUseGetMeQuery.mockReturnValue({
      data: { data: mockUser },
    });

    mockUseGetDashboardSummaryQuery.mockReturnValue({
      data: mockApiResponse(mockDashboardData),
      isLoading: false,
      error: null,
    });

    mockUseGetRecentActivitiesQuery.mockReturnValue({
      data: mockApiResponse({ data: mockActivities }),
      isLoading: false,
      error: null,
    });

    mockUseGetAnalyticsInsightsQuery.mockReturnValue({
      data: mockApiResponse(mockInsights),
      isLoading: false,
      error: null,
    });
  });

  afterEach(() => {
    cleanupTests();
    jest.clearAllMocks();
  });

  it('renders analytics overview correctly', async () => {
    render(<AnalyticsOverview filters={mockFilters} />);

    await waitFor(() => {
      // Check key metrics cards
      expect(screen.getByText('Total Students')).toBeInTheDocument();
      expect(screen.getByText('1,234')).toBeInTheDocument();
      
      expect(screen.getByText('Total Revenue')).toBeInTheDocument();
      expect(screen.getByText('$45,231')).toBeInTheDocument();
      
      expect(screen.getByText('Active Courses')).toBeInTheDocument();
      expect(screen.getByText('24')).toBeInTheDocument();
      
      expect(screen.getByText('Average Rating')).toBeInTheDocument();
      expect(screen.getByText('4.7')).toBeInTheDocument();
    });
  });

  it('displays trend indicators', async () => {
    render(<AnalyticsOverview filters={mockFilters} />);

    await waitFor(() => {
      // Should show percentage changes (mocked as positive trends)
      const trendIndicators = screen.getAllByText(/\d+%/);
      expect(trendIndicators.length).toBeGreaterThan(0);
    });
  });

  it('renders enrollment trend chart', async () => {
    render(<AnalyticsOverview filters={mockFilters} />);

    await waitFor(() => {
      expect(screen.getByText('Enrollment Trend')).toBeInTheDocument();
      // Chart container should be present
      expect(screen.getByRole('img', { hidden: true })).toBeInTheDocument();
    });
  });

  it('renders course performance chart', async () => {
    render(<AnalyticsOverview filters={mockFilters} />);

    await waitFor(() => {
      expect(screen.getByText('Course Performance')).toBeInTheDocument();
    });
  });

  it('displays recent activities', async () => {
    render(<AnalyticsOverview filters={mockFilters} />);

    await waitFor(() => {
      expect(screen.getByText('Recent Activity')).toBeInTheDocument();
      expect(screen.getByText('New Student Enrollment')).toBeInTheDocument();
      expect(screen.getByText('Course Published')).toBeInTheDocument();
    });
  });

  it('shows activity descriptions and timestamps', async () => {
    render(<AnalyticsOverview filters={mockFilters} />);

    await waitFor(() => {
      expect(screen.getByText('John Smith enrolled in React Fundamentals')).toBeInTheDocument();
      expect(screen.getByText('Advanced JavaScript course has been published')).toBeInTheDocument();
      
      // Should show relative timestamps
      const timeElements = screen.getAllByText(/ago$/);
      expect(timeElements.length).toBeGreaterThan(0);
    });
  });

  it('displays priority badges for high priority activities', async () => {
    render(<AnalyticsOverview filters={mockFilters} />);

    await waitFor(() => {
      expect(screen.getByText('High')).toBeInTheDocument();
    });
  });

  it('displays analytics insights', async () => {
    render(<AnalyticsOverview filters={mockFilters} />);

    await waitFor(() => {
      expect(screen.getByText('Key Insights')).toBeInTheDocument();
      expect(screen.getByText('Increase Course Engagement')).toBeInTheDocument();
      expect(screen.getByText('High Completion Rate')).toBeInTheDocument();
    });
  });

  it('shows insight impact and confidence levels', async () => {
    render(<AnalyticsOverview filters={mockFilters} />);

    await waitFor(() => {
      expect(screen.getByText('high impact')).toBeInTheDocument();
      expect(screen.getByText('medium impact')).toBeInTheDocument();
      expect(screen.getByText('85% confidence')).toBeInTheDocument();
      expect(screen.getByText('92% confidence')).toBeInTheDocument();
    });
  });

  it('shows loading state', () => {
    mockUseGetDashboardSummaryQuery.mockReturnValue({
      data: null,
      isLoading: true,
      error: null,
    });

    render(<AnalyticsOverview filters={mockFilters} />);

    // Should show skeleton loaders
    const skeletons = screen.getAllByTestId('skeleton');
    expect(skeletons.length).toBeGreaterThan(0);
  });

  it('shows error state when dashboard fails to load', () => {
    mockUseGetDashboardSummaryQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: { message: 'Failed to load' },
    });

    render(<AnalyticsOverview filters={mockFilters} />);

    expect(screen.getByText('Failed to load analytics')).toBeInTheDocument();
    expect(screen.getByText('Please try refreshing the page')).toBeInTheDocument();
  });

  it('handles empty activities gracefully', async () => {
    mockUseGetRecentActivitiesQuery.mockReturnValue({
      data: mockApiResponse({ data: [] }),
      isLoading: false,
      error: null,
    });

    render(<AnalyticsOverview filters={mockFilters} />);

    await waitFor(() => {
      expect(screen.getByText('Recent Activity')).toBeInTheDocument();
      // Should not crash with empty activities
    });
  });

  it('handles empty insights gracefully', async () => {
    mockUseGetAnalyticsInsightsQuery.mockReturnValue({
      data: mockApiResponse([]),
      isLoading: false,
      error: null,
    });

    render(<AnalyticsOverview filters={mockFilters} />);

    await waitFor(() => {
      expect(screen.getByText('Key Insights')).toBeInTheDocument();
      // Should not crash with empty insights
    });
  });

  it('applies filters correctly', () => {
    const customFilters: AnalyticsFilters = {
      period: 'weekly',
      startDate: '2024-02-01',
      endDate: '2024-02-07',
      courseId: 'course-123',
    };

    render(<AnalyticsOverview filters={customFilters} />);

    // Verify that the queries are called with the correct parameters
    expect(mockUseGetDashboardSummaryQuery).toHaveBeenCalledWith(
      mockUser._id,
      { skip: false }
    );
  });

  it('formats numbers correctly', async () => {
    render(<AnalyticsOverview filters={mockFilters} />);

    await waitFor(() => {
      // Should format large numbers with commas
      expect(screen.getByText('1,234')).toBeInTheDocument();
      expect(screen.getByText('$45,231')).toBeInTheDocument();
    });
  });
});
