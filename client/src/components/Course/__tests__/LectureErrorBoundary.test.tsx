import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import LectureErrorBoundary from '../LectureErrorBoundary';

// Mock console.error to avoid noise in tests
const originalError = console.error;
beforeEach(() => {
  console.error = vi.fn();
});

afterEach(() => {
  console.error = originalError;
});

// Component that throws an error
const ThrowError = ({ shouldThrow = false }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

// Component that throws a network error
const ThrowNetworkError = ({ shouldThrow = false }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('Network error: Failed to fetch');
  }
  return <div>No error</div>;
};

// Component that throws a permission error
const ThrowPermissionError = ({ shouldThrow = false }: { shouldThrow?: boolean }) => {
  if (shouldThrow) {
    throw new Error('403 Unauthorized access');
  }
  return <div>No error</div>;
};

describe('LectureErrorBoundary', () => {
  const defaultProps = {
    lectureId: 'lecture-123',
    courseId: 'course-456'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      writable: true,
      value: true
    });
    // Mock clipboard API
    Object.assign(navigator, {
      clipboard: {
        writeText: vi.fn().mockResolvedValue(undefined)
      }
    });
  });

  it('renders children when there is no error', () => {
    render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowError shouldThrow={false} />
      </LectureErrorBoundary>
    );

    expect(screen.getByText('No error')).toBeInTheDocument();
  });

  it('renders error UI when child component throws', () => {
    render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    expect(screen.getByText('Lecture Loading Error')).toBeInTheDocument();
    expect(screen.getByText('Test error')).toBeInTheDocument();
  });

  it('identifies network errors correctly', () => {
    render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowNetworkError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    expect(screen.getByText('Network Error')).toBeInTheDocument();
    expect(screen.getByText('Check Your Internet Connection')).toBeInTheDocument();
  });

  it('identifies permission errors correctly', () => {
    render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowPermissionError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    expect(screen.getByText('Permission Error')).toBeInTheDocument();
    expect(screen.getByText('Access Denied')).toBeInTheDocument();
  });

  it('shows retry button for retryable errors', () => {
    render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    expect(screen.getByText(/retry/i)).toBeInTheDocument();
  });

  it('handles retry functionality', async () => {
    const onRetry = vi.fn();
    
    const { rerender } = render(
      <LectureErrorBoundary {...defaultProps} onRetry={onRetry}>
        <ThrowError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    fireEvent.click(screen.getByText(/retry/i));

    await waitFor(() => {
      expect(onRetry).toHaveBeenCalled();
    });

    // Simulate successful retry by re-rendering without error
    rerender(
      <LectureErrorBoundary {...defaultProps} onRetry={onRetry}>
        <ThrowError shouldThrow={false} />
      </LectureErrorBoundary>
    );

    expect(screen.getByText('No error')).toBeInTheDocument();
  });

  it('limits retry attempts', () => {
    const { rerender } = render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    // Click retry multiple times
    for (let i = 0; i < 5; i++) {
      const retryButton = screen.queryByText(/retry/i);
      if (retryButton) {
        fireEvent.click(retryButton);
        // Re-render with error to simulate failed retry
        rerender(
          <LectureErrorBoundary {...defaultProps}>
            <ThrowError shouldThrow={true} />
          </LectureErrorBoundary>
        );
      }
    }

    // After max retries, retry button should be disabled or not shown
    expect(screen.queryByText(/retry \(0 left\)/i)).toBeInTheDocument();
  });

  it('shows offline indicator when offline', () => {
    Object.defineProperty(navigator, 'onLine', { value: false });
    
    render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowNetworkError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    expect(screen.getByText('Offline')).toBeInTheDocument();
  });

  it('handles go back functionality', () => {
    const onNavigateBack = vi.fn();
    
    render(
      <LectureErrorBoundary {...defaultProps} onNavigateBack={onNavigateBack}>
        <ThrowError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    fireEvent.click(screen.getByText('Go Back'));
    expect(onNavigateBack).toHaveBeenCalled();
  });

  it('handles refresh page functionality', () => {
    // Mock window.location.reload
    const mockReload = vi.fn();
    Object.defineProperty(window, 'location', {
      value: { reload: mockReload },
      writable: true
    });

    render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    fireEvent.click(screen.getByText('Refresh Page'));
    expect(mockReload).toHaveBeenCalled();
  });

  it('handles error report generation', async () => {
    render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    fireEvent.click(screen.getByText('Copy Error Report'));

    await waitFor(() => {
      expect(navigator.clipboard.writeText).toHaveBeenCalled();
    });
  });

  it('shows error ID for support', () => {
    render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    expect(screen.getByText(/error id:/i)).toBeInTheDocument();
  });

  it('renders custom fallback when provided', () => {
    const customFallback = <div>Custom error message</div>;
    
    render(
      <LectureErrorBoundary {...defaultProps} fallback={customFallback}>
        <ThrowError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    expect(screen.getByText('Custom error message')).toBeInTheDocument();
  });

  it('auto-retries network errors when coming back online', async () => {
    // Start offline
    Object.defineProperty(navigator, 'onLine', { value: false });
    
    render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowNetworkError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    expect(screen.getByText('Offline')).toBeInTheDocument();

    // Simulate coming back online
    Object.defineProperty(navigator, 'onLine', { value: true });
    window.dispatchEvent(new Event('online'));

    // Should attempt auto-retry
    await waitFor(() => {
      // The component should attempt to retry automatically
      expect(screen.getByText('Network Error')).toBeInTheDocument();
    });
  });

  it('provides appropriate solutions for different error types', () => {
    // Test network error solutions
    const { rerender } = render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowNetworkError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    expect(screen.getByText('Check Your Internet Connection')).toBeInTheDocument();

    // Test permission error solutions
    rerender(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowPermissionError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    expect(screen.getByText('Access Denied')).toBeInTheDocument();
    expect(screen.getByText('Go to Course Page')).toBeInTheDocument();
  });

  it('handles component unmounting during retry', () => {
    const { unmount } = render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    // Click retry
    fireEvent.click(screen.getByText(/retry/i));

    // Unmount component
    unmount();

    // Should not throw any errors
    expect(true).toBe(true);
  });

  it('logs errors with proper context', () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    expect(consoleSpy).toHaveBeenCalled();
    consoleSpy.mockRestore();
  });

  it('handles window history back when no onNavigateBack provided', () => {
    const mockBack = vi.fn();
    Object.defineProperty(window, 'history', {
      value: { back: mockBack },
      writable: true
    });

    render(
      <LectureErrorBoundary {...defaultProps}>
        <ThrowError shouldThrow={true} />
      </LectureErrorBoundary>
    );

    fireEvent.click(screen.getByText('Go Back'));
    expect(mockBack).toHaveBeenCalled();
  });
});
