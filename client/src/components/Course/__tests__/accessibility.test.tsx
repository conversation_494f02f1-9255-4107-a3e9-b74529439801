import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { axe, toHaveNoViolations } from 'jest-axe';
import CloudinaryVideoPlayer from '../CloudinaryVideoPlayer';
import UnifiedContentViewer from '../UnifiedContentViewer';
import LectureErrorBoundary from '../LectureErrorBoundary';
import { ILecture } from '@/types/course';
import { Toaster } from '@/components/ui/toaster';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock ReactPlayer
vi.mock('react-player', () => ({
  default: ({ onReady }: any) => {
    React.useEffect(() => {
      setTimeout(() => onReady?.(), 100);
    }, [onReady]);
    return <div data-testid="react-player" role="application" aria-label="Video player" />;
  },
  canPlay: vi.fn(() => true)
}));

// Create mock store
const createMockStore = () => configureStore({
  reducer: {
    player: (state = { positions: {} }) => state
  }
});

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore();
  return render(
    <Provider store={store}>
      {component}
      <Toaster />
    </Provider>
  );
};

describe('Accessibility Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('CloudinaryVideoPlayer Accessibility', () => {
    const defaultProps = {
      src: 'https://example.com/video.mp4',
      videoId: 'test-video-123',
      poster: 'https://example.com/poster.jpg'
    };

    it('should not have accessibility violations', async () => {
      const { container } = renderWithProvider(
        <CloudinaryVideoPlayer {...defaultProps} />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('has proper ARIA labels for controls', () => {
      renderWithProvider(<CloudinaryVideoPlayer {...defaultProps} />);

      expect(screen.getByLabelText(/play video/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/video progress/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/mute/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/enter fullscreen/i)).toBeInTheDocument();
    });

    it('supports keyboard navigation', () => {
      renderWithProvider(<CloudinaryVideoPlayer {...defaultProps} />);

      const playButton = screen.getByLabelText(/play video/i);
      
      // Should be focusable
      playButton.focus();
      expect(document.activeElement).toBe(playButton);

      // Should respond to Enter key
      fireEvent.keyDown(playButton, { key: 'Enter' });
      expect(screen.getByLabelText(/pause video/i)).toBeInTheDocument();

      // Should respond to Space key
      fireEvent.keyDown(playButton, { key: ' ' });
      expect(screen.getByLabelText(/play video/i)).toBeInTheDocument();
    });

    it('provides screen reader announcements', () => {
      renderWithProvider(<CloudinaryVideoPlayer {...defaultProps} />);

      const playButton = screen.getByLabelText(/play video/i);
      fireEvent.click(playButton);

      // Check for aria-live announcements
      const announcements = document.querySelectorAll('[aria-live="polite"]');
      expect(announcements.length).toBeGreaterThan(0);
    });

    it('has proper focus management', () => {
      renderWithProvider(<CloudinaryVideoPlayer {...defaultProps} />);

      const controls = [
        screen.getByLabelText(/play video/i),
        screen.getByLabelText(/video progress/i),
        screen.getByLabelText(/mute/i),
        screen.getByLabelText(/enter fullscreen/i)
      ];

      controls.forEach(control => {
        control.focus();
        expect(document.activeElement).toBe(control);
      });
    });

    it('supports high contrast mode', () => {
      renderWithProvider(<CloudinaryVideoPlayer {...defaultProps} />);

      // Check that controls have sufficient contrast
      const playButton = screen.getByLabelText(/play video/i);
      const styles = window.getComputedStyle(playButton);
      
      // Should have visible borders or backgrounds for high contrast
      expect(playButton).toHaveClass(/text-white|border/);
    });

    it('respects reduced motion preferences', () => {
      // Mock prefers-reduced-motion
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: vi.fn().mockImplementation(query => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: vi.fn(),
          removeListener: vi.fn(),
          addEventListener: vi.fn(),
          removeEventListener: vi.fn(),
          dispatchEvent: vi.fn(),
        })),
      });

      renderWithProvider(<CloudinaryVideoPlayer {...defaultProps} />);

      // Should not have animations when reduced motion is preferred
      const container = screen.getByTestId('react-player').parentElement;
      expect(container).not.toHaveClass(/animate-/);
    });
  });

  describe('UnifiedContentViewer Accessibility', () => {
    const mockLecture: ILecture = {
      _id: 'lecture-123',
      lectureTitle: 'Test Lecture',
      instruction: 'This is a test lecture',
      videoUrl: 'https://example.com/video.mp4',
      pdfUrl: 'https://example.com/document.pdf',
      duration: 3600,
      isPreviewFree: false,
      courseId: 'course-123',
      order: 1
    };

    it('should not have accessibility violations', async () => {
      const { container } = renderWithProvider(
        <UnifiedContentViewer lecture={mockLecture} />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('has proper heading structure', () => {
      renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);

      const heading = screen.getByText('Lecture Content');
      expect(heading.tagName).toBe('H3');
    });

    it('has accessible tab navigation', () => {
      renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);

      const videoTab = screen.getByText('Video');
      const pdfTab = screen.getByText('Pdf');

      // Tabs should be focusable
      videoTab.focus();
      expect(document.activeElement).toBe(videoTab);

      // Should support arrow key navigation
      fireEvent.keyDown(videoTab, { key: 'ArrowRight' });
      expect(document.activeElement).toBe(pdfTab);

      fireEvent.keyDown(pdfTab, { key: 'ArrowLeft' });
      expect(document.activeElement).toBe(videoTab);
    });

    it('provides proper ARIA attributes for content panels', () => {
      renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);

      // Content panels should have proper ARIA attributes
      const videoPanel = screen.getByTestId('react-player').closest('[role="tabpanel"]');
      expect(videoPanel).toHaveAttribute('aria-labelledby');
    });

    it('has accessible download buttons', () => {
      renderWithProvider(
        <UnifiedContentViewer lecture={mockLecture} enableDownload={true} />
      );

      const downloadButton = screen.getByTitle('Download content');
      expect(downloadButton).toHaveAttribute('aria-label');
      expect(downloadButton).toBeInTheDocument();
    });

    it('provides alternative text for content types', () => {
      renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);

      // Should have descriptive text for different content types
      expect(screen.getByText('HLS')).toBeInTheDocument();
      expect(screen.getByText('Downloadable')).toBeInTheDocument();
    });
  });

  describe('LectureErrorBoundary Accessibility', () => {
    const ThrowError = () => {
      throw new Error('Test error');
    };

    it('should not have accessibility violations', async () => {
      const { container } = render(
        <LectureErrorBoundary lectureId="test" courseId="test">
          <ThrowError />
        </LectureErrorBoundary>
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('has proper heading structure in error state', () => {
      render(
        <LectureErrorBoundary lectureId="test" courseId="test">
          <ThrowError />
        </LectureErrorBoundary>
      );

      const heading = screen.getByText('Lecture Loading Error');
      expect(heading.tagName).toBe('H3');
    });

    it('has accessible error messages', () => {
      render(
        <LectureErrorBoundary lectureId="test" courseId="test">
          <ThrowError />
        </LectureErrorBoundary>
      );

      // Error message should be announced to screen readers
      const errorMessage = screen.getByText('Test error');
      expect(errorMessage.closest('[role="alert"]')).toBeInTheDocument();
    });

    it('has keyboard accessible action buttons', () => {
      render(
        <LectureErrorBoundary lectureId="test" courseId="test">
          <ThrowError />
        </LectureErrorBoundary>
      );

      const retryButton = screen.getByText(/retry/i);
      const goBackButton = screen.getByText('Go Back');

      // Buttons should be focusable and have proper labels
      retryButton.focus();
      expect(document.activeElement).toBe(retryButton);

      goBackButton.focus();
      expect(document.activeElement).toBe(goBackButton);
    });

    it('provides clear error context', () => {
      render(
        <LectureErrorBoundary lectureId="test" courseId="test">
          <ThrowError />
        </LectureErrorBoundary>
      );

      // Should provide clear information about the error
      expect(screen.getByText('Unexpected Error')).toBeInTheDocument();
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
    });

    it('has proper color contrast for error states', () => {
      render(
        <LectureErrorBoundary lectureId="test" courseId="test">
          <ThrowError />
        </LectureErrorBoundary>
      );

      const errorIcon = screen.getByRole('img', { hidden: true });
      expect(errorIcon).toHaveClass(/text-red-/);
    });
  });

  describe('Keyboard Navigation', () => {
    it('supports tab navigation through all interactive elements', () => {
      const mockLecture: ILecture = {
        _id: 'lecture-123',
        lectureTitle: 'Test Lecture',
        instruction: 'This is a test lecture',
        videoUrl: 'https://example.com/video.mp4',
        pdfUrl: 'https://example.com/document.pdf',
        duration: 3600,
        isPreviewFree: false,
        courseId: 'course-123',
        order: 1
      };

      renderWithProvider(
        <UnifiedContentViewer lecture={mockLecture} enableDownload={true} />
      );

      // Get all focusable elements
      const focusableElements = screen.getAllByRole('button');
      
      // Should be able to tab through all elements
      focusableElements.forEach(element => {
        element.focus();
        expect(document.activeElement).toBe(element);
      });
    });

    it('has proper focus indicators', () => {
      renderWithProvider(
        <CloudinaryVideoPlayer 
          src="https://example.com/video.mp4"
          videoId="test"
        />
      );

      const playButton = screen.getByLabelText(/play video/i);
      playButton.focus();

      // Should have visible focus indicator
      expect(playButton).toHaveClass(/focus:/);
    });
  });

  describe('Screen Reader Support', () => {
    it('provides meaningful labels for all interactive elements', () => {
      renderWithProvider(
        <CloudinaryVideoPlayer 
          src="https://example.com/video.mp4"
          videoId="test"
        />
      );

      // All buttons should have accessible names
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(
          button.getAttribute('aria-label') || 
          button.textContent ||
          button.getAttribute('title')
        ).toBeTruthy();
      });
    });

    it('announces state changes', () => {
      renderWithProvider(
        <CloudinaryVideoPlayer 
          src="https://example.com/video.mp4"
          videoId="test"
        />
      );

      const playButton = screen.getByLabelText(/play video/i);
      fireEvent.click(playButton);

      // Should create announcement elements
      const announcements = document.querySelectorAll('[aria-live]');
      expect(announcements.length).toBeGreaterThan(0);
    });
  });

  describe('Color and Contrast', () => {
    it('maintains sufficient color contrast', () => {
      renderWithProvider(
        <CloudinaryVideoPlayer 
          src="https://example.com/video.mp4"
          videoId="test"
        />
      );

      // Controls should have sufficient contrast
      const controls = screen.getAllByRole('button');
      controls.forEach(control => {
        const styles = window.getComputedStyle(control);
        // Should have contrasting colors (this is a simplified check)
        expect(control).toHaveClass(/text-white|text-black|text-gray-/);
      });
    });

    it('does not rely solely on color for information', () => {
      const mockLecture: ILecture = {
        _id: 'lecture-123',
        lectureTitle: 'Test Lecture',
        instruction: 'This is a test lecture',
        videoUrl: 'https://example.com/video.mp4',
        duration: 3600,
        isPreviewFree: false,
        courseId: 'course-123',
        order: 1
      };

      renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);

      // Status indicators should have text labels, not just colors
      const badges = screen.getAllByText(/HLS|Downloadable/);
      expect(badges.length).toBeGreaterThan(0);
    });
  });
});
