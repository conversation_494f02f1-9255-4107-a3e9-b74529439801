import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import CloudinaryVideoPlayer from '../CloudinaryVideoPlayer';
import { Toaster } from '@/components/ui/toaster';

// Mock ReactPlayer
vi.mock('react-player', () => ({
  default: vi.fn(({ onPlay, onPause, onProgress, onDuration, onEnded, onReady, onError, onBuffer, onBufferEnd }) => {
    React.useEffect(() => {
      // Simulate player ready
      setTimeout(() => onReady?.(), 100);
    }, [onReady]);

    return (
      <div data-testid="react-player">
        <button 
          data-testid="play-button" 
          onClick={() => onPlay?.()}
        >
          Play
        </button>
        <button 
          data-testid="pause-button" 
          onClick={() => onPause?.()}
        >
          Pause
        </button>
        <button 
          data-testid="progress-button" 
          onClick={() => onProgress?.({ played: 0.5, playedSeconds: 30, loaded: 0.8, loadedSeconds: 48 })}
        >
          Progress
        </button>
        <button 
          data-testid="duration-button" 
          onClick={() => onDuration?.(60)}
        >
          Duration
        </button>
        <button 
          data-testid="ended-button" 
          onClick={() => onEnded?.()}
        >
          Ended
        </button>
        <button 
          data-testid="error-button" 
          onClick={() => onError?.({ target: { error: { code: 2 } } })}
        >
          Error
        </button>
        <button 
          data-testid="buffer-button" 
          onClick={() => onBuffer?.()}
        >
          Buffer
        </button>
        <button 
          data-testid="buffer-end-button" 
          onClick={() => onBufferEnd?.()}
        >
          Buffer End
        </button>
      </div>
    );
  }),
  canPlay: vi.fn(() => true)
}));

// Mock Redux store
const createMockStore = () => configureStore({
  reducer: {
    player: (state = { positions: {} }, action) => {
      if (action.type === 'player/setLastPosition') {
        return {
          ...state,
          positions: {
            ...state.positions,
            [action.payload.videoId]: action.payload.position
          }
        };
      }
      return state;
    }
  }
});

// Mock navigator.onLine
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true
});

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore();
  return render(
    <Provider store={store}>
      {component}
      <Toaster />
    </Provider>
  );
};

describe('CloudinaryVideoPlayer', () => {
  const defaultProps = {
    src: 'https://example.com/video.mp4',
    videoId: 'test-video-123',
    poster: 'https://example.com/poster.jpg'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    navigator.onLine = true;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders video player correctly', () => {
    renderWithProvider(<CloudinaryVideoPlayer {...defaultProps} />);
    
    expect(screen.getByTestId('react-player')).toBeInTheDocument();
  });

  it('handles play/pause functionality', async () => {
    const onTimeUpdate = vi.fn();
    renderWithProvider(
      <CloudinaryVideoPlayer {...defaultProps} onTimeUpdate={onTimeUpdate} />
    );

    // Test play
    fireEvent.click(screen.getByTestId('play-button'));
    await waitFor(() => {
      expect(screen.getByLabelText(/pause video/i)).toBeInTheDocument();
    });

    // Test pause
    fireEvent.click(screen.getByTestId('pause-button'));
    await waitFor(() => {
      expect(screen.getByLabelText(/play video/i)).toBeInTheDocument();
    });
  });

  it('handles progress updates and saves position', async () => {
    const onTimeUpdate = vi.fn();
    renderWithProvider(
      <CloudinaryVideoPlayer {...defaultProps} onTimeUpdate={onTimeUpdate} />
    );

    // Simulate duration first
    fireEvent.click(screen.getByTestId('duration-button'));
    
    // Simulate progress
    fireEvent.click(screen.getByTestId('progress-button'));
    
    await waitFor(() => {
      expect(onTimeUpdate).toHaveBeenCalledWith(30);
    });
  });

  it('handles video completion', async () => {
    const onComplete = vi.fn();
    renderWithProvider(
      <CloudinaryVideoPlayer {...defaultProps} onComplete={onComplete} />
    );

    fireEvent.click(screen.getByTestId('ended-button'));
    
    await waitFor(() => {
      expect(onComplete).toHaveBeenCalled();
    });
  });

  it('handles video errors with retry mechanism', async () => {
    const onError = vi.fn();
    renderWithProvider(
      <CloudinaryVideoPlayer 
        {...defaultProps} 
        onError={onError}
        autoRetry={true}
        maxRetries={2}
      />
    );

    // Trigger error
    fireEvent.click(screen.getByTestId('error-button'));
    
    await waitFor(() => {
      expect(screen.getByText(/video error/i)).toBeInTheDocument();
    });

    // Should show retry button
    expect(screen.getByText(/retry/i)).toBeInTheDocument();
  });

  it('handles offline state correctly', async () => {
    // Mock offline state
    Object.defineProperty(navigator, 'onLine', { value: false, writable: true });
    
    renderWithProvider(<CloudinaryVideoPlayer {...defaultProps} />);
    
    // Trigger offline event
    act(() => {
      window.dispatchEvent(new Event('offline'));
    });

    await waitFor(() => {
      expect(screen.getByText(/offline/i)).toBeInTheDocument();
    });
  });

  it('supports multiple video resolutions', () => {
    const videoResolutions = [
      { url: 'https://example.com/720p.mp4', quality: '720p', format: 'mp4' },
      { url: 'https://example.com/1080p.mp4', quality: '1080p', format: 'mp4' }
    ];

    renderWithProvider(
      <CloudinaryVideoPlayer 
        {...defaultProps} 
        videoResolutions={videoResolutions}
      />
    );

    // Should show quality selector
    expect(screen.getByLabelText(/video quality/i)).toBeInTheDocument();
    expect(screen.getByDisplayValue('Auto')).toBeInTheDocument();
  });

  it('handles HLS streaming', () => {
    renderWithProvider(
      <CloudinaryVideoPlayer 
        {...defaultProps} 
        hlsUrl="https://example.com/stream.m3u8"
      />
    );

    expect(screen.getByTestId('react-player')).toBeInTheDocument();
  });

  it('shows download button when enabled', () => {
    renderWithProvider(
      <CloudinaryVideoPlayer 
        {...defaultProps} 
        enableDownload={true}
      />
    );

    expect(screen.getByLabelText(/download video/i)).toBeInTheDocument();
  });

  it('handles buffering states', async () => {
    renderWithProvider(<CloudinaryVideoPlayer {...defaultProps} />);

    // Start buffering
    fireEvent.click(screen.getByTestId('buffer-button'));
    
    await waitFor(() => {
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    // End buffering
    fireEvent.click(screen.getByTestId('buffer-end-button'));
    
    await waitFor(() => {
      expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
    });
  });

  it('supports keyboard controls', async () => {
    renderWithProvider(<CloudinaryVideoPlayer {...defaultProps} />);

    // Test spacebar for play/pause
    fireEvent.keyDown(window, { key: ' ' });
    
    // Test arrow keys for seeking
    fireEvent.keyDown(window, { key: 'ArrowRight' });
    fireEvent.keyDown(window, { key: 'ArrowLeft' });
    
    // Test 'f' for fullscreen
    fireEvent.keyDown(window, { key: 'f' });
    
    // Test 'm' for mute
    fireEvent.keyDown(window, { key: 'm' });
    
    // Test number keys for seeking to percentage
    fireEvent.keyDown(window, { key: '5' });
  });

  it('handles analytics tracking when enabled', async () => {
    const consoleSpy = vi.spyOn(console, 'info').mockImplementation(() => {});
    
    renderWithProvider(
      <CloudinaryVideoPlayer 
        {...defaultProps} 
        enableAnalytics={true}
      />
    );

    // Simulate play to start analytics
    fireEvent.click(screen.getByTestId('play-button'));
    
    // Wait for analytics to be tracked
    await new Promise(resolve => setTimeout(resolve, 1100));
    
    consoleSpy.mockRestore();
  });

  it('handles initial position correctly', async () => {
    const initialPosition = 30;
    renderWithProvider(
      <CloudinaryVideoPlayer 
        {...defaultProps} 
        initialPosition={initialPosition}
      />
    );

    // Wait for player to be ready
    await waitFor(() => {
      expect(screen.getByTestId('react-player')).toBeInTheDocument();
    });
  });

  it('shows accessibility announcements', async () => {
    renderWithProvider(<CloudinaryVideoPlayer {...defaultProps} />);

    // Play video
    fireEvent.click(screen.getByTestId('play-button'));
    
    // Should announce play state
    await waitFor(() => {
      const announcements = document.querySelectorAll('[aria-live="polite"]');
      expect(announcements.length).toBeGreaterThan(0);
    });
  });

  it('handles volume controls', async () => {
    renderWithProvider(<CloudinaryVideoPlayer {...defaultProps} />);

    // Find and click mute button
    const muteButton = screen.getByLabelText(/mute/i);
    fireEvent.click(muteButton);
    
    await waitFor(() => {
      expect(screen.getByLabelText(/unmute/i)).toBeInTheDocument();
    });
  });

  it('handles fullscreen toggle', async () => {
    renderWithProvider(<CloudinaryVideoPlayer {...defaultProps} />);

    // Mock fullscreen API
    document.exitFullscreen = vi.fn();
    HTMLElement.prototype.requestFullscreen = vi.fn();

    const fullscreenButton = screen.getByLabelText(/enter fullscreen/i);
    fireEvent.click(fullscreenButton);
    
    expect(HTMLElement.prototype.requestFullscreen).toHaveBeenCalled();
  });

  it('handles network errors appropriately', async () => {
    renderWithProvider(
      <CloudinaryVideoPlayer 
        {...defaultProps} 
        autoRetry={true}
      />
    );

    // Simulate network error
    fireEvent.click(screen.getByTestId('error-button'));
    
    await waitFor(() => {
      expect(screen.getByText(/network error/i)).toBeInTheDocument();
    });
  });
});
