import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import UnifiedContentViewer from '../UnifiedContentViewer';
import { ILecture } from '@/types/course';
import { Toaster } from '@/components/ui/toaster';

// Mock CloudinaryVideoPlayer
vi.mock('../CloudinaryVideoPlayer', () => ({
  default: ({ src, onError }: any) => (
    <div data-testid="video-player">
      <div>Video Source: {src}</div>
      <button onClick={() => onError?.(new Error('Video error'))}>
        Trigger Error
      </button>
    </div>
  )
}));

// Mock Redux store
const createMockStore = () => configureStore({
  reducer: {
    player: (state = { positions: {} }) => state
  }
});

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore();
  return render(
    <Provider store={store}>
      {component}
      <Toaster />
    </Provider>
  );
};

describe('UnifiedContentViewer', () => {
  const mockLecture: ILecture = {
    _id: 'lecture-123',
    lectureTitle: 'Test Lecture',
    instruction: 'This is a test lecture',
    videoUrl: 'https://example.com/video.mp4',
    pdfUrl: 'https://example.com/document.pdf',
    duration: 3600,
    isPreviewFree: false,
    courseId: 'course-123',
    order: 1,
    thumbnailUrl: 'https://example.com/thumbnail.jpg',
    videoResolutions: [
      { url: 'https://example.com/720p.mp4', quality: '720p', format: 'mp4' },
      { url: 'https://example.com/1080p.mp4', quality: '1080p', format: 'mp4' }
    ],
    hlsUrl: 'https://example.com/stream.m3u8'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders video content by default', () => {
    renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);
    
    expect(screen.getByText('Lecture Content')).toBeInTheDocument();
    expect(screen.getByTestId('video-player')).toBeInTheDocument();
    expect(screen.getByText('Video Source: https://example.com/stream.m3u8')).toBeInTheDocument();
  });

  it('shows content type tabs when multiple content types available', () => {
    renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);
    
    expect(screen.getByText('Video')).toBeInTheDocument();
    expect(screen.getByText('Pdf')).toBeInTheDocument();
  });

  it('switches between content types', async () => {
    renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);
    
    // Initially shows video
    expect(screen.getByTestId('video-player')).toBeInTheDocument();
    
    // Click PDF tab
    fireEvent.click(screen.getByText('Pdf'));
    
    await waitFor(() => {
      expect(screen.getByTitle(mockLecture.lectureTitle + ' - Materials')).toBeInTheDocument();
    });
  });

  it('shows download button when enabled', () => {
    renderWithProvider(
      <UnifiedContentViewer 
        lecture={mockLecture} 
        enableDownload={true}
      />
    );
    
    expect(screen.getByTitle('Download content')).toBeInTheDocument();
  });

  it('handles download functionality', async () => {
    const mockOnDownload = vi.fn();
    renderWithProvider(
      <UnifiedContentViewer 
        lecture={mockLecture} 
        enableDownload={true}
        onDownload={mockOnDownload}
      />
    );
    
    fireEvent.click(screen.getByTitle('Download content'));
    
    await waitFor(() => {
      expect(mockOnDownload).toHaveBeenCalledWith(
        mockLecture.hlsUrl,
        expect.stringContaining('Video')
      );
    });
  });

  it('shows content metadata badges', () => {
    renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);
    
    expect(screen.getByText('HLS')).toBeInTheDocument();
    expect(screen.getByText('Downloadable')).toBeInTheDocument();
  });

  it('handles PDF preview toggle', async () => {
    renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);
    
    // Switch to PDF tab
    fireEvent.click(screen.getByText('Pdf'));
    
    await waitFor(() => {
      expect(screen.getByTitle('Hide preview')).toBeInTheDocument();
    });
    
    // Toggle preview off
    fireEvent.click(screen.getByTitle('Hide preview'));
    
    await waitFor(() => {
      expect(screen.getByText('PDF preview is disabled')).toBeInTheDocument();
      expect(screen.getByText('Show Preview')).toBeInTheDocument();
    });
  });

  it('handles video error gracefully', async () => {
    renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);
    
    // Trigger video error
    fireEvent.click(screen.getByText('Trigger Error'));
    
    await waitFor(() => {
      expect(screen.getByText(/video error/i)).toBeInTheDocument();
    });
  });

  it('handles lecture with only video content', () => {
    const videoOnlyLecture = {
      ...mockLecture,
      pdfUrl: undefined
    };
    
    renderWithProvider(<UnifiedContentViewer lecture={videoOnlyLecture} />);
    
    // Should not show content type tabs
    expect(screen.queryByText('Pdf')).not.toBeInTheDocument();
    expect(screen.getByTestId('video-player')).toBeInTheDocument();
  });

  it('handles lecture with only PDF content', () => {
    const pdfOnlyLecture = {
      ...mockLecture,
      videoUrl: undefined,
      hlsUrl: undefined,
      videoResolutions: undefined
    };
    
    renderWithProvider(<UnifiedContentViewer lecture={pdfOnlyLecture} />);
    
    // Should show PDF content
    expect(screen.getByTitle(pdfOnlyLecture.lectureTitle + ' - Materials')).toBeInTheDocument();
  });

  it('handles lecture with no content', () => {
    const emptyLecture = {
      ...mockLecture,
      videoUrl: undefined,
      hlsUrl: undefined,
      videoResolutions: undefined,
      pdfUrl: undefined
    };
    
    renderWithProvider(<UnifiedContentViewer lecture={emptyLecture} />);
    
    expect(screen.getByText('No content available for this lecture')).toBeInTheDocument();
  });

  it('shows appropriate format badges for different content types', () => {
    renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);
    
    // Video should show HLS format
    expect(screen.getByText('HLS')).toBeInTheDocument();
    
    // Switch to PDF
    fireEvent.click(screen.getByText('Pdf'));
    
    // PDF should show PDF format
    expect(screen.getByText('PDF')).toBeInTheDocument();
  });

  it('handles iframe loading errors for PDF', async () => {
    renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);
    
    // Switch to PDF tab
    fireEvent.click(screen.getByText('Pdf'));
    
    await waitFor(() => {
      const iframe = screen.getByTitle(mockLecture.lectureTitle + ' - Materials');
      expect(iframe).toBeInTheDocument();
      
      // Simulate iframe error
      fireEvent.error(iframe);
    });
    
    await waitFor(() => {
      expect(screen.getByText(/pdf load error/i)).toBeInTheDocument();
    });
  });

  it('handles download when not available', async () => {
    const nonDownloadableLecture = {
      ...mockLecture,
      videoUrl: undefined // Make it non-downloadable
    };
    
    renderWithProvider(
      <UnifiedContentViewer 
        lecture={nonDownloadableLecture} 
        enableDownload={true}
      />
    );
    
    // Should not show download button for non-downloadable content
    expect(screen.queryByTitle('Download content')).not.toBeInTheDocument();
  });

  it('uses video resolutions when available', () => {
    const lectureWithResolutions = {
      ...mockLecture,
      hlsUrl: undefined, // Remove HLS to test resolution fallback
      videoUrl: undefined
    };
    
    renderWithProvider(<UnifiedContentViewer lecture={lectureWithResolutions} />);
    
    expect(screen.getByTestId('video-player')).toBeInTheDocument();
    expect(screen.getByText('Video Source: https://example.com/720p.mp4')).toBeInTheDocument();
  });

  it('falls back to videoUrl when no HLS or resolutions', () => {
    const basicVideoLecture = {
      ...mockLecture,
      hlsUrl: undefined,
      videoResolutions: undefined
    };
    
    renderWithProvider(<UnifiedContentViewer lecture={basicVideoLecture} />);
    
    expect(screen.getByTestId('video-player')).toBeInTheDocument();
    expect(screen.getByText('Video Source: https://example.com/video.mp4')).toBeInTheDocument();
  });

  it('handles custom download handler', async () => {
    const customDownloadHandler = vi.fn();
    renderWithProvider(
      <UnifiedContentViewer 
        lecture={mockLecture} 
        enableDownload={true}
        onDownload={customDownloadHandler}
      />
    );
    
    fireEvent.click(screen.getByTitle('Download content'));
    
    await waitFor(() => {
      expect(customDownloadHandler).toHaveBeenCalled();
    });
  });

  it('shows loading state appropriately', () => {
    renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);
    
    // Component should render without loading state for immediate content
    expect(screen.getByTestId('video-player')).toBeInTheDocument();
  });

  it('handles accessibility features', () => {
    renderWithProvider(<UnifiedContentViewer lecture={mockLecture} />);
    
    // Check for proper ARIA labels
    expect(screen.getByTitle('Download content')).toBeInTheDocument();
    expect(screen.getByTitle('Hide preview')).toBeInTheDocument();
  });
});
