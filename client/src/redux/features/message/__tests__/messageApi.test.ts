import { configureStore } from '@reduxjs/toolkit';
import { baseApi } from '@/redux/api/baseApi';
import { messageApi } from '../messageApi';
import { FolderType, MessageType, MessagePriority } from '@/types/message';
import { createMockMessage, createMockThread, mockApiResponse, mockPaginatedResponse } from '@/test-utils';

// Mock fetch
global.fetch = jest.fn();

describe('Message API', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        [baseApi.reducerPath]: baseApi.reducer,
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware().concat(baseApi.middleware),
    });

    (fetch as jest.Mock).mockClear();
  });

  afterEach(() => {
    store.dispatch(baseApi.util.resetApiState());
  });

  describe('getMessagesByFolder', () => {
    it('should fetch messages by folder successfully', async () => {
      const mockThreads = [createMockThread(), createMockThread({ _id: 'thread-2' })];
      const mockResponse = mockApiResponse(mockPaginatedResponse(mockThreads));

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await store.dispatch(
        messageApi.endpoints.getMessagesByFolder.initiate({
          userId: 'user-123',
          folderType: FolderType.INBOX,
          page: 1,
          limit: 20,
        })
      );

      expect(result.data).toEqual({
        success: true,
        message: 'Success',
        data: {
          data: mockThreads,
          pagination: expect.any(Object),
        },
      });

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/messages/users/user-123/threads'),
        expect.objectContaining({
          method: 'GET',
        })
      );
    });

    it('should include filters in query parameters', async () => {
      const mockResponse = mockApiResponse(mockPaginatedResponse([]));

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      await store.dispatch(
        messageApi.endpoints.getMessagesByFolder.initiate({
          userId: 'user-123',
          folderType: FolderType.INBOX,
          filters: {
            messageType: MessageType.DIRECT,
            priority: MessagePriority.HIGH,
            isRead: false,
            searchQuery: 'test search',
          },
        })
      );

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('messageType=direct'),
        expect.any(Object)
      );
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('priority=high'),
        expect.any(Object)
      );
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('isRead=false'),
        expect.any(Object)
      );
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('search=test%20search'),
        expect.any(Object)
      );
    });
  });

  describe('getThreadMessages', () => {
    it('should fetch thread messages successfully', async () => {
      const mockMessages = [createMockMessage(), createMockMessage({ _id: 'message-2' })];
      const mockResponse = mockApiResponse(mockPaginatedResponse(mockMessages));

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await store.dispatch(
        messageApi.endpoints.getThreadMessages.initiate({
          threadId: 'thread-123',
          page: 1,
          limit: 50,
        })
      );

      expect(result.data).toEqual({
        success: true,
        message: 'Success',
        data: {
          data: mockMessages,
          pagination: expect.any(Object),
        },
      });

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/messages/threads/thread-123/messages'),
        expect.objectContaining({
          method: 'GET',
        })
      );
    });
  });

  describe('sendMessage', () => {
    it('should send message successfully', async () => {
      const mockMessage = createMockMessage();
      const mockResponse = mockApiResponse(mockMessage);

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const messageData = {
        recipientIds: ['user-456'],
        subject: 'Test Subject',
        content: 'Test content',
        messageType: MessageType.DIRECT,
        priority: MessagePriority.NORMAL,
        attachments: [],
        isDraft: false,
      };

      const result = await store.dispatch(
        messageApi.endpoints.sendMessage.initiate(messageData)
      );

      expect(result.data).toEqual({
        success: true,
        message: 'Success',
        data: mockMessage,
      });

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/messages/send'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(messageData),
        })
      );
    });
  });

  describe('replyToMessage', () => {
    it('should reply to message successfully', async () => {
      const mockMessage = createMockMessage();
      const mockResponse = mockApiResponse(mockMessage);

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const replyData = {
        threadId: 'thread-123',
        content: 'Reply content',
        attachments: [],
      };

      const result = await store.dispatch(
        messageApi.endpoints.replyToMessage.initiate(replyData)
      );

      expect(result.data).toEqual({
        success: true,
        message: 'Success',
        data: mockMessage,
      });

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/messages/threads/thread-123/reply'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({
            content: replyData.content,
            attachments: replyData.attachments,
          }),
        })
      );
    });
  });

  describe('markThreadAsRead', () => {
    it('should mark thread as read successfully', async () => {
      const mockResponse = mockApiResponse(null);

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await store.dispatch(
        messageApi.endpoints.markThreadAsRead.initiate({
          threadId: 'thread-123',
        })
      );

      expect(result.data).toEqual({
        success: true,
        message: 'Success',
        data: null,
      });

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/messages/threads/thread-123/mark-read'),
        expect.objectContaining({
          method: 'PATCH',
        })
      );
    });
  });

  describe('toggleMessageStar', () => {
    it('should toggle message star successfully', async () => {
      const mockMessage = createMockMessage({ isStarred: true });
      const mockResponse = mockApiResponse(mockMessage);

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await store.dispatch(
        messageApi.endpoints.toggleMessageStar.initiate({
          messageId: 'message-123',
          isStarred: true,
        })
      );

      expect(result.data).toEqual({
        success: true,
        message: 'Success',
        data: mockMessage,
      });

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/messages/message-123/star'),
        expect.objectContaining({
          method: 'PATCH',
          body: JSON.stringify({ isStarred: true }),
        })
      );
    });
  });

  describe('searchMessages', () => {
    it('should search messages successfully', async () => {
      const mockSearchResults = [
        {
          message: createMockMessage(),
          thread: createMockThread(),
          highlights: {
            subject: 'Test',
            content: 'search term',
          },
          relevanceScore: 0.95,
        },
      ];
      const mockResponse = mockApiResponse(mockPaginatedResponse(mockSearchResults));

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await store.dispatch(
        messageApi.endpoints.searchMessages.initiate({
          userId: 'user-123',
          query: 'test search',
          page: 1,
          limit: 20,
        })
      );

      expect(result.data).toEqual({
        success: true,
        message: 'Success',
        data: {
          data: mockSearchResults,
          pagination: expect.any(Object),
        },
      });

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/messages/users/user-123/search'),
        expect.objectContaining({
          method: 'GET',
        })
      );
    });
  });

  describe('saveDraft', () => {
    it('should save draft successfully', async () => {
      const mockDraft = {
        _id: 'draft-123',
        recipientIds: ['user-456'],
        subject: 'Draft Subject',
        content: 'Draft content',
        messageType: MessageType.DIRECT,
        priority: MessagePriority.NORMAL,
        attachments: [],
        isDraft: true,
        autoSavedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      const mockResponse = mockApiResponse(mockDraft);

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const draftData = {
        recipientIds: ['user-456'],
        subject: 'Draft Subject',
        content: 'Draft content',
        messageType: MessageType.DIRECT,
        priority: MessagePriority.NORMAL,
        attachments: [],
        isDraft: true,
      };

      const result = await store.dispatch(
        messageApi.endpoints.saveDraft.initiate(draftData)
      );

      expect(result.data).toEqual({
        success: true,
        message: 'Success',
        data: mockDraft,
      });

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/messages/drafts'),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(draftData),
        })
      );
    });

    it('should update existing draft', async () => {
      const mockDraft = {
        _id: 'draft-123',
        recipientIds: ['user-456'],
        subject: 'Updated Draft Subject',
        content: 'Updated draft content',
        messageType: MessageType.DIRECT,
        priority: MessagePriority.NORMAL,
        attachments: [],
        isDraft: true,
        autoSavedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      const mockResponse = mockApiResponse(mockDraft);

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const draftData = {
        draftId: 'draft-123',
        subject: 'Updated Draft Subject',
        content: 'Updated draft content',
      };

      const result = await store.dispatch(
        messageApi.endpoints.saveDraft.initiate(draftData)
      );

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/messages/drafts/draft-123'),
        expect.objectContaining({
          method: 'PUT',
        })
      );
    });
  });
});
