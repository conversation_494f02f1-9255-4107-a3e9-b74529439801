import { renderHook, act } from '@testing-library/react';
import {
  useFocusManagement,
  useKeyboardNavigation,
  useScreenReader,
  useReducedMotion,
  useAriaAttributes,
} from '../useAccessibility';
import { setupTests, cleanupTests } from '@/test-utils';

describe('Accessibility Hooks', () => {
  beforeEach(() => {
    setupTests();
  });

  afterEach(() => {
    cleanupTests();
  });

  describe('useFocusManagement', () => {
    it('should provide focus management utilities', () => {
      const { result } = renderHook(() => useFocusManagement());

      expect(result.current.trapFocus).toBeInstanceOf(Function);
      expect(result.current.restoreFocus).toBeInstanceOf(Function);
    });

    it('should trap focus within container', () => {
      const { result } = renderHook(() => useFocusManagement());

      // Create a mock container with focusable elements
      const container = document.createElement('div');
      const button1 = document.createElement('button');
      const button2 = document.createElement('button');
      const input = document.createElement('input');

      button1.textContent = 'Button 1';
      button2.textContent = 'Button 2';
      input.type = 'text';

      container.appendChild(button1);
      container.appendChild(input);
      container.appendChild(button2);
      document.body.appendChild(container);

      // Mock focus method
      button1.focus = jest.fn();
      button2.focus = jest.fn();
      input.focus = jest.fn();

      const cleanup = result.current.trapFocus(container);

      // Should focus first element
      expect(button1.focus).toHaveBeenCalled();

      // Cleanup
      cleanup();
      document.body.removeChild(container);
    });

    it('should restore focus to element', () => {
      const { result } = renderHook(() => useFocusManagement());

      const element = document.createElement('button');
      element.focus = jest.fn();

      result.current.restoreFocus(element);

      expect(element.focus).toHaveBeenCalled();
    });
  });

  describe('useKeyboardNavigation', () => {
    const mockItems = ['item1', 'item2', 'item3'];
    const mockOnSelect = jest.fn();

    beforeEach(() => {
      mockOnSelect.mockClear();
    });

    it('should initialize with correct default values', () => {
      const { result } = renderHook(() =>
        useKeyboardNavigation(mockItems, mockOnSelect)
      );

      expect(result.current.activeIndex).toBe(-1);
      expect(result.current.handleKeyDown).toBeInstanceOf(Function);
      expect(result.current.setActiveIndex).toBeInstanceOf(Function);
    });

    it('should handle arrow down navigation', () => {
      const { result } = renderHook(() =>
        useKeyboardNavigation(mockItems, mockOnSelect)
      );

      const event = new KeyboardEvent('keydown', { key: 'ArrowDown' });
      event.preventDefault = jest.fn();

      act(() => {
        result.current.handleKeyDown(event);
      });

      expect(event.preventDefault).toHaveBeenCalled();
      expect(result.current.activeIndex).toBe(0);
    });

    it('should handle arrow up navigation', () => {
      const { result } = renderHook(() =>
        useKeyboardNavigation(mockItems, mockOnSelect)
      );

      // Set initial active index
      act(() => {
        result.current.setActiveIndex(1);
      });

      const event = new KeyboardEvent('keydown', { key: 'ArrowUp' });
      event.preventDefault = jest.fn();

      act(() => {
        result.current.handleKeyDown(event);
      });

      expect(event.preventDefault).toHaveBeenCalled();
      expect(result.current.activeIndex).toBe(0);
    });

    it('should handle Enter key selection', () => {
      const { result } = renderHook(() =>
        useKeyboardNavigation(mockItems, mockOnSelect)
      );

      // Set active index
      act(() => {
        result.current.setActiveIndex(1);
      });

      const event = new KeyboardEvent('keydown', { key: 'Enter' });
      event.preventDefault = jest.fn();

      act(() => {
        result.current.handleKeyDown(event);
      });

      expect(event.preventDefault).toHaveBeenCalled();
      expect(mockOnSelect).toHaveBeenCalledWith(1);
    });

    it('should handle Home key navigation', () => {
      const { result } = renderHook(() =>
        useKeyboardNavigation(mockItems, mockOnSelect)
      );

      // Set initial active index
      act(() => {
        result.current.setActiveIndex(2);
      });

      const event = new KeyboardEvent('keydown', { key: 'Home' });
      event.preventDefault = jest.fn();

      act(() => {
        result.current.handleKeyDown(event);
      });

      expect(event.preventDefault).toHaveBeenCalled();
      expect(result.current.activeIndex).toBe(0);
    });

    it('should handle End key navigation', () => {
      const { result } = renderHook(() =>
        useKeyboardNavigation(mockItems, mockOnSelect)
      );

      const event = new KeyboardEvent('keydown', { key: 'End' });
      event.preventDefault = jest.fn();

      act(() => {
        result.current.handleKeyDown(event);
      });

      expect(event.preventDefault).toHaveBeenCalled();
      expect(result.current.activeIndex).toBe(2);
    });

    it('should loop navigation when enabled', () => {
      const { result } = renderHook(() =>
        useKeyboardNavigation(mockItems, mockOnSelect, { loop: true })
      );

      // Set to last item
      act(() => {
        result.current.setActiveIndex(2);
      });

      const event = new KeyboardEvent('keydown', { key: 'ArrowDown' });
      event.preventDefault = jest.fn();

      act(() => {
        result.current.handleKeyDown(event);
      });

      expect(result.current.activeIndex).toBe(0);
    });

    it('should handle horizontal navigation', () => {
      const { result } = renderHook(() =>
        useKeyboardNavigation(mockItems, mockOnSelect, { orientation: 'horizontal' })
      );

      const event = new KeyboardEvent('keydown', { key: 'ArrowRight' });
      event.preventDefault = jest.fn();

      act(() => {
        result.current.handleKeyDown(event);
      });

      expect(event.preventDefault).toHaveBeenCalled();
      expect(result.current.activeIndex).toBe(0);
    });
  });

  describe('useScreenReader', () => {
    it('should provide announce function', () => {
      const { result } = renderHook(() => useScreenReader());

      expect(result.current.announce).toBeInstanceOf(Function);
    });

    it('should create live region on mount', () => {
      renderHook(() => useScreenReader());

      const liveRegion = document.querySelector('[aria-live="polite"]');
      expect(liveRegion).toBeInTheDocument();
    });

    it('should announce messages', () => {
      const { result } = renderHook(() => useScreenReader());

      act(() => {
        result.current.announce('Test announcement');
      });

      const liveRegion = document.querySelector('[aria-live="polite"]');
      expect(liveRegion?.textContent).toBe('Test announcement');
    });

    it('should handle assertive announcements', () => {
      const { result } = renderHook(() => useScreenReader());

      act(() => {
        result.current.announce('Urgent announcement', 'assertive');
      });

      const liveRegion = document.querySelector('[aria-live="assertive"]');
      expect(liveRegion).toBeInTheDocument();
    });
  });

  describe('useReducedMotion', () => {
    it('should detect reduced motion preference', () => {
      // Mock matchMedia to return reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });

      const { result } = renderHook(() => useReducedMotion());

      expect(result.current).toBe(true);
    });

    it('should return false when reduced motion is not preferred', () => {
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: false,
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });

      const { result } = renderHook(() => useReducedMotion());

      expect(result.current).toBe(false);
    });
  });

  describe('useAriaAttributes', () => {
    it('should generate unique IDs', () => {
      const { result } = renderHook(() => useAriaAttributes());

      const id1 = result.current.generateId('test');
      const id2 = result.current.generateId('test');

      expect(id1).toMatch(/^test-/);
      expect(id2).toMatch(/^test-/);
      expect(id1).not.toBe(id2);
    });

    it('should create ARIA props object', () => {
      const { result } = renderHook(() => useAriaAttributes());

      const ariaProps = result.current.createAriaProps({
        label: 'Test label',
        expanded: true,
        selected: false,
        disabled: true,
        required: true,
        invalid: false,
        role: 'button',
      });

      expect(ariaProps).toEqual({
        'aria-label': 'Test label',
        'aria-expanded': true,
        'aria-selected': false,
        'aria-disabled': true,
        'aria-required': true,
        'aria-invalid': false,
        'role': 'button',
      });
    });

    it('should omit undefined values from ARIA props', () => {
      const { result } = renderHook(() => useAriaAttributes());

      const ariaProps = result.current.createAriaProps({
        label: 'Test label',
        expanded: undefined,
        selected: undefined,
      });

      expect(ariaProps).toEqual({
        'aria-label': 'Test label',
      });
    });
  });
});
