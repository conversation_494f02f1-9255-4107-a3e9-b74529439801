import { renderHook, act, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useRetryMechanism, useApiWithRetry } from '../useRetryMechanism';

// Mock timers
vi.useFakeTimers();

describe('useRetryMechanism', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
  });

  it('executes function successfully without retry', async () => {
    const { result } = renderHook(() => useRetryMechanism());
    const mockFn = vi.fn().mockResolvedValue('success');

    let promise: Promise<string>;
    act(() => {
      promise = result.current.executeWithRetry(mockFn);
    });

    await act(async () => {
      const value = await promise!;
      expect(value).toBe('success');
    });

    expect(mockFn).toHaveBeenCalledTimes(1);
    expect(result.current.retryCount).toBe(0);
    expect(result.current.isRetrying).toBe(false);
  });

  it('retries on retryable errors', async () => {
    const { result } = renderHook(() => useRetryMechanism({
      maxRetries: 2,
      baseDelay: 100
    }));

    const mockFn = vi.fn()
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValue('success');

    let promise: Promise<string>;
    act(() => {
      promise = result.current.executeWithRetry(mockFn);
    });

    // Fast-forward through the delay
    act(() => {
      vi.advanceTimersByTime(200);
    });

    await act(async () => {
      const value = await promise!;
      expect(value).toBe('success');
    });

    expect(mockFn).toHaveBeenCalledTimes(2);
  });

  it('respects maxRetries limit', async () => {
    const { result } = renderHook(() => useRetryMechanism({
      maxRetries: 2,
      baseDelay: 100
    }));

    const mockFn = vi.fn().mockRejectedValue(new Error('Persistent error'));

    let promise: Promise<string>;
    let error: Error | null = null;

    act(() => {
      promise = result.current.executeWithRetry(mockFn);
    });

    // Fast-forward through all retry delays
    act(() => {
      vi.advanceTimersByTime(1000);
    });

    await act(async () => {
      try {
        await promise!;
      } catch (err) {
        error = err as Error;
      }
    });

    expect(error?.message).toBe('Persistent error');
    expect(mockFn).toHaveBeenCalledTimes(3); // Initial + 2 retries
    expect(result.current.canRetry).toBe(false);
  });

  it('calculates exponential backoff correctly', async () => {
    const { result } = renderHook(() => useRetryMechanism({
      maxRetries: 3,
      baseDelay: 100,
      backoffFactor: 2
    }));

    const mockFn = vi.fn().mockRejectedValue(new Error('Network error'));
    const delays: number[] = [];

    // Mock setTimeout to capture delays
    const originalSetTimeout = global.setTimeout;
    global.setTimeout = vi.fn((callback, delay) => {
      delays.push(delay as number);
      return originalSetTimeout(callback, 0);
    }) as any;

    let promise: Promise<string>;
    act(() => {
      promise = result.current.executeWithRetry(mockFn);
    });

    act(() => {
      vi.runAllTimers();
    });

    await act(async () => {
      try {
        await promise!;
      } catch {
        // Expected to fail
      }
    });

    // Check that delays follow exponential backoff pattern
    expect(delays.length).toBeGreaterThan(0);
    expect(delays[0]).toBeGreaterThanOrEqual(100); // First retry: ~100ms
    expect(delays[1]).toBeGreaterThanOrEqual(200); // Second retry: ~200ms
    expect(delays[2]).toBeGreaterThanOrEqual(400); // Third retry: ~400ms

    global.setTimeout = originalSetTimeout;
  });

  it('calls onRetry callback', async () => {
    const onRetry = vi.fn();
    const { result } = renderHook(() => useRetryMechanism({
      maxRetries: 2,
      baseDelay: 100,
      onRetry
    }));

    const mockFn = vi.fn()
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValue('success');

    let promise: Promise<string>;
    act(() => {
      promise = result.current.executeWithRetry(mockFn);
    });

    act(() => {
      vi.advanceTimersByTime(200);
    });

    await act(async () => {
      await promise!;
    });

    expect(onRetry).toHaveBeenCalledWith(1, expect.any(Error));
  });

  it('calls onMaxRetriesReached callback', async () => {
    const onMaxRetriesReached = vi.fn();
    const { result } = renderHook(() => useRetryMechanism({
      maxRetries: 1,
      baseDelay: 100,
      onMaxRetriesReached
    }));

    const mockFn = vi.fn().mockRejectedValue(new Error('Persistent error'));

    let promise: Promise<string>;
    act(() => {
      promise = result.current.executeWithRetry(mockFn);
    });

    act(() => {
      vi.advanceTimersByTime(200);
    });

    await act(async () => {
      try {
        await promise!;
      } catch {
        // Expected to fail
      }
    });

    expect(onMaxRetriesReached).toHaveBeenCalledWith(expect.any(Error));
  });

  it('respects custom retry condition', async () => {
    const { result } = renderHook(() => useRetryMechanism({
      maxRetries: 2,
      baseDelay: 100,
      retryCondition: (error) => !error.message.includes('permanent')
    }));

    const mockFn = vi.fn().mockRejectedValue(new Error('permanent failure'));

    let promise: Promise<string>;
    let error: Error | null = null;

    act(() => {
      promise = result.current.executeWithRetry(mockFn);
    });

    await act(async () => {
      try {
        await promise!;
      } catch (err) {
        error = err as Error;
      }
    });

    expect(error?.message).toBe('permanent failure');
    expect(mockFn).toHaveBeenCalledTimes(1); // No retries due to condition
  });

  it('handles abort signal', async () => {
    const { result } = renderHook(() => useRetryMechanism());
    const mockFn = vi.fn().mockImplementation((signal) => {
      return new Promise((resolve, reject) => {
        signal?.addEventListener('abort', () => reject(new Error('Aborted')));
        setTimeout(resolve, 1000);
      });
    });

    let promise: Promise<any>;
    act(() => {
      promise = result.current.executeWithRetry(mockFn);
    });

    act(() => {
      result.current.abort();
    });

    await act(async () => {
      try {
        await promise!;
      } catch (error) {
        expect((error as Error).message).toBe('Aborted');
      }
    });
  });

  it('resets state correctly', () => {
    const { result } = renderHook(() => useRetryMechanism());

    act(() => {
      // Simulate some state changes
      result.current.executeWithRetry(() => Promise.reject(new Error('test')));
    });

    act(() => {
      result.current.reset();
    });

    expect(result.current.retryCount).toBe(0);
    expect(result.current.isRetrying).toBe(false);
    expect(result.current.lastError).toBe(null);
    expect(result.current.canRetry).toBe(true);
  });

  it('provides utility functions', () => {
    const { result } = renderHook(() => useRetryMechanism({
      maxRetries: 3,
      baseDelay: 100
    }));

    expect(typeof result.current.isNetworkError).toBe('function');
    expect(typeof result.current.getNextRetryDelay).toBe('function');
    expect(typeof result.current.getRemainingRetries).toBe('function');

    expect(result.current.getRemainingRetries()).toBe(3);
  });
});

describe('useApiWithRetry', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    global.fetch = vi.fn();
  });

  it('makes successful API calls', async () => {
    const mockResponse = { data: 'test' };
    (global.fetch as any).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    });

    const { result } = renderHook(() => useApiWithRetry());

    let response: any;
    await act(async () => {
      response = await result.current.apiCall('/test');
    });

    expect(response).toEqual(mockResponse);
    expect(global.fetch).toHaveBeenCalledWith('/test', expect.any(Object));
  });

  it('retries on server errors', async () => {
    (global.fetch as any)
      .mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error'
      })
      .mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({ data: 'success' })
      });

    const { result } = renderHook(() => useApiWithRetry());

    let response: any;
    await act(async () => {
      vi.advanceTimersByTime(1000);
      response = await result.current.apiCall('/test');
    });

    expect(response).toEqual({ data: 'success' });
    expect(global.fetch).toHaveBeenCalledTimes(2);
  });

  it('does not retry on client errors', async () => {
    (global.fetch as any).mockResolvedValue({
      ok: false,
      status: 404,
      statusText: 'Not Found'
    });

    const { result } = renderHook(() => useApiWithRetry());

    let error: Error | null = null;
    await act(async () => {
      try {
        await result.current.apiCall('/test');
      } catch (err) {
        error = err as Error;
      }
    });

    expect(error?.message).toContain('404');
    expect(global.fetch).toHaveBeenCalledTimes(1);
  });

  it('handles network errors', async () => {
    (global.fetch as any).mockRejectedValue(new Error('Network error'));

    const { result } = renderHook(() => useApiWithRetry());

    let error: Error | null = null;
    await act(async () => {
      try {
        await result.current.apiCall('/test');
      } catch (err) {
        error = err as Error;
      }
    });

    expect(error?.message).toBe('Network error');
    expect(global.fetch).toHaveBeenCalledTimes(4); // Initial + 3 retries
  });
});
