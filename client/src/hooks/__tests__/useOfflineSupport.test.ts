import { renderHook, act, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { useOfflineSupport } from '../useOfflineSupport';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  key: vi.fn(),
  length: 0
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock navigator.onLine
Object.defineProperty(navigator, 'onLine', {
  writable: true,
  value: true
});

describe('useOfflineSupport', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    navigator.onLine = true;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('initializes with correct online state', () => {
    const { result } = renderHook(() => useOfflineSupport());
    
    expect(result.current.isOnline).toBe(true);
    expect(result.current.isConnecting).toBe(false);
    expect(result.current.lastOnline).toBeInstanceOf(Date);
  });

  it('handles offline state', () => {
    navigator.onLine = false;
    
    const { result } = renderHook(() => useOfflineSupport());
    
    expect(result.current.isOnline).toBe(false);
    expect(result.current.lastOnline).toBe(null);
  });

  it('responds to online/offline events', async () => {
    const { result } = renderHook(() => useOfflineSupport());
    
    // Simulate going offline
    act(() => {
      navigator.onLine = false;
      window.dispatchEvent(new Event('offline'));
    });
    
    await waitFor(() => {
      expect(result.current.isOnline).toBe(false);
    });
    
    // Simulate coming back online
    act(() => {
      navigator.onLine = true;
      window.dispatchEvent(new Event('online'));
    });
    
    await waitFor(() => {
      expect(result.current.isOnline).toBe(true);
      expect(result.current.lastOnline).toBeInstanceOf(Date);
    });
  });

  it('sets and gets cache correctly', () => {
    const { result } = renderHook(() => useOfflineSupport({
      cachePrefix: 'test_'
    }));
    
    const testData = { id: 1, name: 'test' };
    
    act(() => {
      result.current.setCache('key1', testData);
    });
    
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'test_key1',
      expect.stringContaining('"data":{"id":1,"name":"test"}')
    );
    
    // Mock getting from cache
    localStorageMock.getItem.mockReturnValue(JSON.stringify({
      key: 'key1',
      data: testData,
      timestamp: Date.now(),
      expiresAt: Date.now() + 1000000
    }));
    
    const cached = result.current.getCache('key1');
    expect(cached).toEqual(testData);
  });

  it('handles expired cache entries', () => {
    const { result } = renderHook(() => useOfflineSupport());
    
    // Mock expired cache entry
    localStorageMock.getItem.mockReturnValue(JSON.stringify({
      key: 'key1',
      data: { test: 'data' },
      timestamp: Date.now(),
      expiresAt: Date.now() - 1000 // Expired
    }));
    
    const cached = result.current.getCache('key1');
    expect(cached).toBe(null);
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('lecture_cache_key1');
  });

  it('clears cache correctly', () => {
    const { result } = renderHook(() => useOfflineSupport({
      cachePrefix: 'test_'
    }));
    
    // Mock Object.keys to return some cache keys
    Object.keys = vi.fn().mockReturnValue([
      'test_key1',
      'test_key2',
      'other_key',
      'test_pattern_key'
    ]);
    
    act(() => {
      result.current.clearCache('pattern');
    });
    
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('test_pattern_key');
    expect(localStorageMock.removeItem).not.toHaveBeenCalledWith('test_key1');
    expect(localStorageMock.removeItem).not.toHaveBeenCalledWith('other_key');
  });

  it('queues operations when offline', () => {
    const { result } = renderHook(() => useOfflineSupport());
    
    const operation1 = vi.fn().mockResolvedValue(undefined);
    const operation2 = vi.fn().mockResolvedValue(undefined);
    
    act(() => {
      result.current.queueOperation(operation1);
      result.current.queueOperation(operation2);
    });
    
    expect(result.current.queuedOperationsCount).toBe(2);
  });

  it('syncs queued operations when coming online', async () => {
    const { result } = renderHook(() => useOfflineSupport({
      syncOnReconnect: true
    }));
    
    const operation1 = vi.fn().mockResolvedValue(undefined);
    const operation2 = vi.fn().mockResolvedValue(undefined);
    
    act(() => {
      result.current.queueOperation(operation1);
      result.current.queueOperation(operation2);
    });
    
    // Simulate coming online
    act(() => {
      navigator.onLine = true;
      window.dispatchEvent(new Event('online'));
    });
    
    await waitFor(() => {
      expect(operation1).toHaveBeenCalled();
      expect(operation2).toHaveBeenCalled();
    });
    
    expect(result.current.queuedOperationsCount).toBe(0);
  });

  it('handles failed sync operations', async () => {
    const { result } = renderHook(() => useOfflineSupport());
    
    const failingOperation = vi.fn().mockRejectedValue(new Error('Sync failed'));
    const successOperation = vi.fn().mockResolvedValue(undefined);
    
    act(() => {
      result.current.queueOperation(failingOperation);
      result.current.queueOperation(successOperation);
    });
    
    await act(async () => {
      await result.current.syncQueuedOperations();
    });
    
    expect(failingOperation).toHaveBeenCalled();
    expect(successOperation).toHaveBeenCalled();
    expect(result.current.queuedOperationsCount).toBe(1); // Failed operation re-queued
  });

  it('checks connection with ping', async () => {
    global.fetch = vi.fn().mockResolvedValue({
      ok: true
    });
    
    const { result } = renderHook(() => useOfflineSupport());
    
    let isConnected: boolean;
    await act(async () => {
      isConnected = await result.current.checkConnection();
    });
    
    expect(isConnected!).toBe(true);
    expect(global.fetch).toHaveBeenCalledWith('/favicon.ico', expect.any(Object));
  });

  it('handles connection check failure', async () => {
    global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));
    
    const { result } = renderHook(() => useOfflineSupport());
    
    let isConnected: boolean;
    await act(async () => {
      isConnected = await result.current.checkConnection();
    });
    
    expect(isConnected!).toBe(false);
    expect(result.current.isOnline).toBe(false);
  });

  it('gets data with fallback to cache', async () => {
    const { result } = renderHook(() => useOfflineSupport());
    
    const fetchFn = vi.fn().mockResolvedValue({ data: 'fresh' });
    
    let data: any;
    await act(async () => {
      data = await result.current.getDataWithFallback('key1', fetchFn);
    });
    
    expect(data).toEqual({ data: 'fresh' });
    expect(fetchFn).toHaveBeenCalled();
  });

  it('uses cache when offline', async () => {
    navigator.onLine = false;
    
    const { result } = renderHook(() => useOfflineSupport());
    
    // Mock cached data
    localStorageMock.getItem.mockReturnValue(JSON.stringify({
      key: 'key1',
      data: { data: 'cached' },
      timestamp: Date.now(),
      expiresAt: Date.now() + 1000000
    }));
    
    const fetchFn = vi.fn();
    
    let data: any;
    await act(async () => {
      data = await result.current.getDataWithFallback('key1', fetchFn);
    });
    
    expect(data).toEqual({ data: 'cached' });
    expect(fetchFn).not.toHaveBeenCalled();
  });

  it('falls back to cache when fetch fails', async () => {
    const { result } = renderHook(() => useOfflineSupport());
    
    const fetchFn = vi.fn().mockRejectedValue(new Error('Fetch failed'));
    
    // Mock cached data
    localStorageMock.getItem.mockReturnValue(JSON.stringify({
      key: 'key1',
      data: { data: 'cached' },
      timestamp: Date.now(),
      expiresAt: Date.now() + 1000000
    }));
    
    let data: any;
    await act(async () => {
      data = await result.current.getDataWithFallback('key1', fetchFn);
    });
    
    expect(data).toEqual({ data: 'cached' });
    expect(fetchFn).toHaveBeenCalled();
  });

  it('determines connection quality correctly', () => {
    // Mock connection API
    Object.defineProperty(navigator, 'connection', {
      value: { effectiveType: '4g' },
      writable: true
    });
    
    const { result } = renderHook(() => useOfflineSupport());
    
    expect(result.current.connectionQuality).toBe('excellent');
    
    // Test different connection types
    (navigator as any).connection.effectiveType = '3g';
    const { result: result2 } = renderHook(() => useOfflineSupport());
    expect(result2.current.connectionQuality).toBe('good');
    
    (navigator as any).connection.effectiveType = '2g';
    const { result: result3 } = renderHook(() => useOfflineSupport());
    expect(result3.current.connectionQuality).toBe('poor');
  });

  it('detects slow connections', () => {
    Object.defineProperty(navigator, 'connection', {
      value: { effectiveType: '2g' },
      writable: true
    });
    
    const { result } = renderHook(() => useOfflineSupport());
    
    expect(result.current.isSlowConnection()).toBe(true);
  });

  it('calculates cache size', () => {
    const { result } = renderHook(() => useOfflineSupport({
      cachePrefix: 'test_'
    }));
    
    // Mock localStorage keys and values
    Object.keys = vi.fn().mockReturnValue(['test_key1', 'test_key2', 'other_key']);
    localStorageMock.getItem
      .mockReturnValueOnce('{"data": "test1"}') // ~15 bytes
      .mockReturnValueOnce('{"data": "test2"}') // ~15 bytes
      .mockReturnValueOnce(null);
    
    const size = result.current.getCacheSize();
    expect(size).toBeGreaterThan(0);
  });

  it('cleans up cache when size exceeds limit', () => {
    const { result } = renderHook(() => useOfflineSupport({
      cachePrefix: 'test_',
      maxCacheSize: 0.001 // Very small limit to trigger cleanup
    }));
    
    // Mock large cache entries
    Object.keys = vi.fn().mockReturnValue(['test_key1', 'test_key2']);
    localStorageMock.getItem
      .mockReturnValue(JSON.stringify({
        key: 'key1',
        data: 'x'.repeat(1000), // Large data
        timestamp: Date.now() - 1000
      }));
    
    act(() => {
      result.current.cleanupCache();
    });
    
    expect(localStorageMock.removeItem).toHaveBeenCalled();
  });
});
